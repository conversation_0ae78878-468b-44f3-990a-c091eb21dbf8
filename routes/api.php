<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\CameraController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Integration\IntegrationUserController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
 */

# các api dưới này ko cần xác thực
Route::post('/auth/login', [AuthController::class, 'login'])->middleware(['throttle:1000,1']);
Route::get('/getCurrentYearByTeamChartData', 'App\Http\Controllers\ContractController@getCurrentYearByTeamChartData');
Route::get('/getDashboardPublic', 'App\Http\Controllers\ContractController@dashboardPublic');
Route::post('/frequently-question', 'App\Http\Controllers\FrequentlyQuestionController@frequentlyQuestionByProject');

Route::post('/camera/syncDataCamAI', [CameraController::class, 'syncDataCamAI']);
Route::post('/verify-account', 'App\Http\Controllers\VerifyAccountController@verifyAccount');
Route::group([
    'middleware' => 'api.auth',
    'namespace' => 'App\Http\Controllers',
], function ($router) {
    # phần auth
    Route::post('/auth/logout', 'AuthController@logout');
    Route::post('/auth/refresh', 'AuthController@refresh');
    Route::get('/auth/user-profile', 'AuthController@userProfile');
    Route::post('/changePassword', 'UserController@changePassword');
    Route::post('/pusher-private', 'AuthController@pusherPrivate');

    # danh mục phòng ban
    Route::get('departments', 'DepartmentController@getAll');
    Route::get('/master/department', 'DepartmentController@index');
    Route::get('/master/department/{id}', 'DepartmentController@show');
    Route::post('/master/department', 'DepartmentController@store')->middleware(['permission:department_add', 'throttle:10,1']);
    Route::post('/master/department-update', 'DepartmentController@update')->middleware(['permission:department_edit', 'throttle:10,1']);

    # danh mục chức vụ
    Route::get('positions', 'PositionController@getPositions');
    Route::get('/master/position', 'PositionController@index');
    Route::get('/master/position/{id}', 'PositionController@show');
    Route::post('/master/position', 'PositionController@store')->middleware(['permission:positions_add', 'throttle:10,1']);
    Route::post('/master/position/edit', 'PositionController@edit')->middleware(['permission:positions_edit', 'throttle:10,1']);
    Route::get('/master/position/by/{department_id}', 'PositionController@getByDepartmentId');
    Route::get('/master/sub-position/by/{department_id}', 'PositionController@getSubPositionByDepartmentId');
    Route::get('/master/leader-position/by/{department_id}', 'PositionController@getLeaderPosition');
    Route::post('/master/position/destroy', 'PositionController@destroy')->middleware('permission:positions_delete');

    # danh mục sản phẩm trong công ty
    Route::get('/master/getLeader', 'PositionController@getLeader');
    Route::get('/team-sale', 'PositionController@getTeamSale');
    Route::get('/master/product', 'ProductController@index');
    Route::post('/master/product/add', 'ProductController@store')->middleware(['permission:product_add', 'throttle:10,1']);
    Route::get('/master/product/show/{id}', 'ProductController@show');
    Route::post('/master/product/edit/{id}', 'ProductController@update');
    Route::get('/master/product/except-ke', 'ProductController@exceptKeCode');
    Route::get('/master/school', 'SchoolController@index')->middleware('permission:school_list_management|school_list');
    Route::post('/master/school/export', 'SchoolController@exportExcel')->middleware('throttle:10,1');
    Route::get('/master/schoolLevel', 'SchoolGroupLevelController@getSchoolLevel');
    Route::get('/product-category', 'ProductCategoryController@index');
    Route::post('/product-category/add', 'ProductCategoryController@store')->middleware(['permission:product_add', 'throttle:10,1']);
    Route::post('/product-category/{id}/edit', 'ProductCategoryController@store')->middleware(['permission:product_edit', 'throttle:10,1']);

    # Cấu hình giá sản phẩm theo thị trường, team
    Route::post('/master/team-sale/{id}/config', 'TeamSaleAreaController@store')->middleware(['permission:product_config_range_for_team', 'throttle:10,1']);
    Route::get('master/team-sale/range-product', 'TeamSaleAreaController@index')->middleware('permission:product_config_range_for_team|product_owner_config_range');
    Route::post('/master/team-sale/owner/custom-config', 'TeamSaleAreaController@ownerUpdateRange')->middleware(['permission:product_owner_config_range', 'throttle:20,1']);
    Route::get('/master/team-sale/{teamId}/areas', 'TeamSaleAreaController@getTeamSaleAreas');
    Route::get('/master/team-sale/range-product-default', 'TeamSaleAreaController@getProductRangeDefault');

    # all tỉnh thành
    Route::get('/master/province/all', 'ProvinceController@all');
    # lấy quận huyện theo tỉnh thành
    Route::get('/master/district/{province_id}', 'DistrictController@index');
    # lấy phường xã theo quận huyện
    Route::get('/master/ward/{district_id}', 'WardController@index');
    # lấy phường xã theo quận huyện
    Route::get('/master/employment_contracts/all', 'EmploymentContractController@all');

    Route::get('/master/province-business/all', 'ProvinceBusinessMarketController@all');
    Route::get('/master/district-business/{province_id}', 'DistrictBusinessController@index');

    # danh sách nhân sự
    Route::get('/user', 'UserController@index');
    Route::post('/user', 'UserController@store')->middleware(['permission:user_add', 'throttle:10,1']);
    Route::get('/user-show/{id}', 'UserController@show');
    Route::post('/user-update', 'UserController@update')->middleware(['permission:user_edit', 'throttle:10,1']);
    Route::post('/user-off', 'UserController@offuser')->middleware(['permission:user_delete', 'throttle:10,1']);
    Route::post('/createEmploymentContract', 'UserController@createEmploymentContract')->middleware(['permission:user_add|user_edit', 'throttle:10,1']);
    Route::post('avatar/uploadImage', 'UserController@uploadImage')->middleware('throttle:10,1');
    Route::get('user-workings', 'UserController@getUserworkings');
    Route::get('/getAllUsers', 'FolderController@getUsers');
    Route::get('/get-user-status', 'UserController@getUserStatus');
    Route::post('/user-family/downloadFileSample', 'UserController@downloadFileSample')->middleware('throttle:10,1');
    Route::post('/user-family/importExcel', 'UserController@importExcel')->middleware('throttle:10,1');

    # chức năng khách hàng
    Route::group(['prefix' => 'clients'], function () {
        Route::get('/type', 'ClientController@getTypeClient')->middleware('throttle:10,1');
        Route::get('/', 'ClientController@index')->middleware('permission:client_list|client_list_management');
        Route::post('/add', 'ClientController@store')->middleware(['permission:client_add', 'throttle:10,1']);
        Route::post('/edit', 'ClientController@update')->middleware(['permission:client_edit', 'throttle:10,1']);
        Route::post('/destroy/{client}', 'ClientController@destroy')->middleware('permission:client_delete');
        Route::get('/details/{client}', 'ClientController@details')->middleware('permission:client_detail|client_list_management');
        Route::post('/export-client', 'ClientController@exportClient');
        Route::post('/downloadFileSample', 'ClientController@downloadFileSample')->middleware('throttle:10,1');
        Route::post('/importExcel', 'ClientController@importExcel')->middleware('throttle:10,1');
        Route::get('/searchClient', 'ClientController@searchClient')->middleware('throttle:10,1');
        Route::post('/client-focal-officer-add/{id}', 'ClientController@ClientFocalOfficerStore')->middleware(['permission:client_focal_officer_add', 'throttle:10,1']);
        Route::post('/client-focal-officer-update/{id}', 'ClientController@ClientFocalOfficerUpdate')->middleware(['permission:client_focal_officer_update', 'throttle:10,1']);
        Route::post('/client-focal-officer-delete/{id}', 'ClientController@ClientFocalOfficerDelete')->middleware(['permission:client_focal_officer_delete', 'throttle:10,1']);
        Route::get('/get-client-details/{id}', 'ClientController@getClientDetail')->middleware(['throttle:10,1']);
    });

    # chức năng hợp đồng
    Route::get('/master/contract', 'ContractController@index')->middleware('permission:contract_list|contract_list_management');
    Route::get('/contract/show/{id}', 'ContractController@show')->middleware(['permission:contract_detail|contract_list_management']);
    Route::post('/master/contract/add', 'ContractController@store')->middleware(['permission:contract_add', 'throttle:10,1']);
    Route::post('/contract/edit/{id}', 'ContractController@update')->middleware(['permission:contract_edit', 'throttle:10,1']);
    Route::post('/contract/destroy/{id}', 'ContractController@destroy')->middleware(['permission:contract_delete', 'throttle:10,1']);
    Route::get('/master/unitPrice', 'UnitPriceController@index');
    Route::post('/importExcel/contractBase', 'ContractController@importContractBase')->middleware(['permission:contract_add', 'throttle:10,1']);
    Route::post('/contract/downloadFileSample', 'ContractController@downloadFileSample')->middleware('throttle:10,1');
    Route::get('/master/getThreeYearChartData', 'ContractController@getThreeYearChartData');
    Route::get('/master/getCurrentYearByProductChartData', 'ContractController@getCurrentYearByProductChartData');
    Route::get('/master/getCurrentYearByTeamChartData', 'ContractController@getCurrentYearByTeamChartData');
    Route::get('/master/getExpenseByTeamChartData', 'ContractController@getExpenseByTeamChartData');
    Route::get('/contract/destroy-file/{id}', 'ContractController@destroyFile')->middleware(['permission:contract_edit']);
    Route::get('/contract/getCompany', 'ContractController@getCompany');
    Route::get('/search/client', 'ClientController@searchForContract')->middleware('permission:client_list|client_list_management');
    Route::get('/master/contract/search-product', 'ProductController@getAllowProductByTeam')->middleware('permission:contract_add|contract_edit');
    Route::post('/master/contract/changeStatus/{id}', 'ContractController@changeStatus')->middleware(['permission: contract_list_management', 'throttle:10,1']);
    Route::post('/request-export-contract/{id}', 'ContractController@requestExportContract');
    Route::post('/confirm-export-contract/{id}', 'ContractController@confirmRequestExportContract')->middleware(['permission:approve_export_contract']);
    Route::post('/reopen-export-contract/{id}', 'ContractController@reopenRequestExportContract')->middleware(['permission:approve_export_contract']);
    Route::post('/export-contract', 'ContractController@exportContract');
    Route::post('/import-tmp-contract-base', 'ImportContractController@importTmpContractBase')->middleware(['permission:import_excel_contract']);
    Route::get('/get-tmp-contract-base-list', 'ImportContractController@getTmpContractBaseList')->middleware(['permission:show_import_contract_list|review_contract']);
    Route::get('/get-tmp-contract-base-detail/{importContractId}', 'ImportContractController@getTmpContractBaseDetail')->middleware(['permission:show_import_contract_list|review_contract|import_excel_contract']);
    Route::get('/get-contract-review/{importContractId}', 'ImportContractController@getContractReview')->middleware(['permission:show_import_contract_list|review_contract|import_excel_contract']);
    Route::post('/contract/review-delete/{importContractId}', 'ImportContractController@deleteTmpContractBase')->middleware(['permission:import_excel_contract']);
    Route::post('/contract/review-reject/{importContractId}', 'ImportContractController@rejectTmpContractBase')->middleware(['permission:review_contract']);
    Route::post('/contract/review-approve/{importContractId}', 'ImportContractController@approveTmpContractBase')->middleware(['permission:review_contract']);
    Route::get('/get-client', 'ImportContractController@getClient');
    Route::post('/send-accountant-review/{importContractId}', 'ImportContractController@sendAccountantReview')->middleware(['permission:import_excel_contract']);
    Route::get('/contract/payment', 'ContractController@paymentList')->middleware('permission:contract_add|import_excel_contract');
    Route::post('/contract/payment-save', 'ContractController@paymentSave')->middleware('permission:contract_add|import_excel_contract');
    Route::get('/get-review-contract-form-list', 'ContractController@contractFormList')->middleware('permission:contract_add|review_contract');
    Route::post('/edit-tmp-contract-base-form/{tmpContractId}', 'ContractController@editTmpContract');
    Route::get('/show-tmp-contract-form/{tmpContractId}', 'ContractController@showTmpContract');
    Route::post('/approve-tmp-contract-form', 'ContractController@approveContractForm')->middleware('permission:review_contract');
    Route::post('/reject-tmp-contract-form', 'ContractController@rejectContractForm')->middleware('permission:review_contract');
    Route::post('/delete-tmp-contract-form', 'ContractController@deleteContractForm')->middleware('permission:contract_add');
    // degree
    Route::get('/master/degree', 'DegreeController@index');

    //expense
    Route::get('/expense', 'ExpenseController@index');
    Route::post('/expense/add', 'ExpenseController@store')->middleware(['permission:expense_add', 'throttle:10,1']);
    Route::get('/expense/show/{id}', 'ExpenseController@show');
    Route::post('/expense/edit/{id}', 'ExpenseController@update')->middleware('permission:expense_edit', 'throttle:10,1');
    Route::post('/expense/destroy', 'ExpenseController@destroy')->middleware('permission:expense_delete', 'throttle:10,1');

    // payment
    Route::get('/payment', 'PaymentController@index');
    Route::post('/export-payment', 'PaymentController@exportPayment');

    # API báo cáo
    Route::post('/report/sale-week', 'ReportController@report_sale_week')->middleware('throttle:10,1');
    Route::post('/export/sale-week', 'ExcelController@report_sale_week')->middleware('throttle:10,1');
    Route::post('/report/sale-product', 'ReportController@report_sale_product')->middleware('throttle:10,1');
    Route::post('/export/sale-product', 'ExcelController@report_sale_product')->middleware('throttle:10,1');
    Route::post('/report/sale-debt', 'ReportController@report_sale_debt')->middleware('throttle:10,1');
    Route::post('/export/sale-debt', 'ExcelController@report_sale_debt')->middleware('throttle:10,1');
    Route::post('/report/sale-expire', 'ReportController@report_sale_expire')->middleware('throttle:10,1');
    Route::post('/export/sale-expire', 'ExcelController@report_sale_expire')->middleware('throttle:10,1');
    Route::post('/report/call-center', 'ReportController@report_call_center')->middleware('throttle:10,1');
    Route::post('/export/call-center', 'ExcelController@report_call_center')->middleware('throttle:10,1');
    Route::get('/report/sales', 'ReportController@report_sales');
    Route::post('/export/sales', 'ExcelController@export_sales')->middleware('throttle:10,1');
    Route::get('/report/team-sales', 'ReportController@report_team_sales');
    Route::post('/export/team-sales', 'ExcelController@export_team_sales')->middleware('throttle:10,1');
    Route::get('/report/actual-sales-and-goal-sales', 'ReportController@report_actual_sales_and_goal_sales');
    Route::post('/export/actual-sales-and-goal-sales', 'ExcelController@export_actual_sales_and_goal_sales')->middleware('throttle:10,1');
    Route::post('/report/report-by-product', 'ReportController@reportByProduct');
    Route::post('/export/report-by-product', 'ReportController@exportByProduct');
    Route::post('/report/report-by-team', 'ReportController@reportByTeam');
    Route::post('/export/report-by-team', 'ReportController@exportByTeam');
    Route::post('/report/report-by-market', 'ReportController@reportByMarket');
    Route::post('/export/report-by-market', 'ReportController@exportByMarket');

    // Phân quyền
    Route::get('/roles', 'RoleController@index')->middleware('throttle:10,1');
    Route::post('/roles/update', 'RoleController@update')->middleware('role:admin', 'throttle:10,1');

    // Quản lý công việc
    Route::group([
        'prefix' => 'task-management',
        'namespace' => 'Tasks'
    ], function () {
        Route::get('/resource', 'ProjectController@getResource');
        Route::group(['prefix' => 'projects'], function () {
            Route::get('/', 'ProjectController@index');
            Route::post('/add', 'ProjectController@store')->middleware(['throttle:10,1']);
            Route::post('/{id}/edit', 'ProjectController@store')->middleware(['throttle:10,1']);
            Route::get('/{id}/delete', 'ProjectController@delete')->middleware(['throttle:10,1']);
            Route::get('/{id}/details', 'ProjectController@details')->middleware(['throttle:10,1']);
            Route::post('/{id}/update-resource', 'ProjectController@updateResource')->middleware(['throttle:10,1']);
            Route::post('/{id}/update-state', 'ProjectController@updateState')->middleware(['throttle:10,1']);
            Route::post('/{id}/update-sort-state', 'ProjectController@updateSortState')->middleware(['throttle:10,1']);
        });

        Route::group(['prefix' => 'tickets'], function () {
            Route::get('/{projectId}/list', 'TicketController@index');
            Route::post('/add', 'TicketController@store')->middleware(['permission:ticket_add', 'throttle:10,1']);
            Route::post('/{id}/edit', 'TicketController@store')->middleware(['throttle:10,1']);
            Route::post('/{id}/update-status', 'TicketController@updateStatus')->middleware(['throttle:10,1']);
            Route::get('/{id}/details', 'TicketController@show');
            Route::post('/{id}/destroy', 'TicketController@destroy')->middleware(['throttle:10,1']);
            Route::post('/ticket/addComment/{id}', 'TicketController@addComment')->middleware('throttle:10,1');
            Route::post('/ticket-destroy-file/{id}', 'TicketController@destroyFile');
            Route::get('/get-ticket-status/{projectId}', 'TicketController@getStatus');
            Route::get('/get-ticket-type', 'TicketController@getType');
            Route::get('/get-ticket-leader-review', 'TicketController@getLeaderReview');
            Route::get('/get-ticket-priority', 'TicketController@getPriority');
            Route::get('/get-ticket-level', 'TicketController@getTicketLevel');
            Route::post('/{id}/addComment', 'TicketController@addComment')->middleware('throttle:10,1');
            Route::post('/destroy-file/{id}', 'TicketController@destroyFile')->middleware('throttle:10,1');
            Route::get('/clients', 'TicketController@getClient');
            Route::post('/{id}/log-work', 'TicketController@logWork')->middleware('throttle:10,1');
            Route::post('/{ticketId}/remove-log-work', 'TicketController@removeWorkLog')->middleware('throttle:10,1');
        });

        Route::group(['prefix' => 'report'], function () {
            Route::get('/{projectId}/gantt', 'ReportController@gantt');
        });
    });

    //wednesday share
    Route::get('/master/time', 'WednesdayShareController@getTime');
    Route::get('/wednesdayShare', 'WednesdayShareController@index');
    Route::post('/wednesdayShare/add', 'WednesdayShareController@store')->middleware('throttle:10,1');
    Route::post('/wednesdayShare/edit/{id}', 'WednesdayShareController@update')->middleware('throttle:10,1');
    Route::get('/wednesdayShare/show/{id}', 'WednesdayShareController@show');
    Route::post('/wednesdayShare/destroy/{id}', 'WednesdayShareController@destroy')->middleware('throttle:10,1');
    Route::get('/wednesdayShare/{department_id}', 'WednesdayShareController@getHearer');
    Route::post('/wednesdayShare/reviews/{id}', 'WednesdayShareController@review')->middleware('throttle:10,1');

    //warehouse
    Route::get('/warehouse', 'WarehouseController@index');
    Route::post('/warehouse/add', 'WarehouseController@store')->middleware('throttle:10,1');
    Route::post('/warehouse/edit/{id}', 'WarehouseController@update')->middleware('throttle:10,1');
    Route::post('/warehouse/destroy/{id}', 'WarehouseController@destroy')->middleware('throttle:10,1');
    Route::get('/warehouse-all', 'WarehouseController@getAll');

    // supplier
    Route::get('/supplier', 'SupplierController@index');
    Route::post('/supplier/add', 'SupplierController@store')->middleware(['permission:supplier_add', 'throttle:10,1']);
    Route::post('/supplier/edit/{id}', 'SupplierController@update')->middleware(['permission:supplier_edit', 'throttle:10,1']);
    Route::post('/supplier/destroy/{id}', 'SupplierController@destroy')->middleware(['permission:supplier_delete', 'throttle:10,1']);
    Route::get('/supplier-all', 'SupplierController@getAll');

    //equipment type
    Route::get('/equipment-type', 'EquipmentTypeController@index');
    Route::get('/get-all-equipment', 'EquipmentTypeController@get_all_equipment');
    Route::post('/equipment-type/add', 'EquipmentTypeController@store')->middleware('throttle:10,1');
    Route::post('/equipment-type/edit/{id}', 'EquipmentTypeController@update')->middleware('throttle:10,1');
    Route::post('/equipment-type/destroy/{id}', 'EquipmentTypeController@destroy')->middleware('throttle:10,1');

    //equipment
    Route::get('/equipment', 'EquipmentController@index');
    Route::post('/equipment/add', 'EquipmentController@store')->middleware('throttle:10,1');
    Route::post('/equipment/edit/{id}', 'EquipmentController@update')->middleware('throttle:10,1');
    Route::post('/equipment/destroy/{id}', 'EquipmentController@destroy')->middleware('throttle:10,1');
    Route::post('/equipment/download-file-template', 'EquipmentController@downloadFileTemplate')->middleware('throttle:10,1');
    Route::post('/importExcelEquipment', 'EquipmentController@importExcel')->middleware('throttle:10,1');

    //call center
    Route::get('/callCenter', 'CallCenterController@index');
    Route::post('/callCenter/chooseClient/{id}', 'CallCenterController@chooseClient')->middleware('throttle:10,1');
    Route::get('/callCenter/show-client/{id}', 'CallCenterController@showClient');

    // Notification
    Route::get('/notification', 'NotificationController@list');
    Route::get('/notification-index', 'NotificationController@index');
    Route::post('/notification/markAsRead/{id}', 'NotificationController@markAsRead')->middleware('throttle:10,1');
    Route::post('/notification/markAsReadAll', 'NotificationController@markAsReadAll')->middleware('throttle:10,1');

    // Holiday
    Route::get('/master/holiday', 'HolidayController@index');
    Route::post('/master/holiday/add', 'HolidayController@store')->middleware(['permission:holiday_add', 'throttle:10,1']);
    Route::post('/master/holiday/update/{holiday}', 'HolidayController@update')->middleware(['permission:holiday_edit', 'throttle:10,1']);
    Route::post('/master/holiday/destroy/{holiday}', 'HolidayController@destroy')->middleware(['permission:holiday_delete', 'throttle:10,1']);
    // work day symbol
    Route::get('/master/work-day-symbol', 'WorkDaySymbolController@index');
    Route::post('/master/work-day-symbol/add', 'WorkDaySymbolController@store')->middleware(['permission:work_day_symbol_add', 'throttle:10,1']);
    Route::post('/master/work-day-symbol/update/{id}', 'WorkDaySymbolController@update')->middleware(['permission:work_day_symbol_edit', 'throttle:10,1']);
    Route::post('/master/work-day-symbol/destroy/{id}', 'WorkDaySymbolController@destroy')->middleware(['permission:work_day_symbol_delete', 'throttle:10,1']);

    Route::get('/getLeaderTeamSales', 'QuarterlyGoalsController@getLeaderTeamSales');
    Route::get('/quarterly-goals', 'QuarterlyGoalsController@index');
    Route::post('/quarterly-goals/add', 'QuarterlyGoalsController@store')->middleware(['permission:quarterly_goal_add', 'throttle:10,1']);
    Route::post('/quarterly-goals/edit/{id}', 'QuarterlyGoalsController@update')->middleware(['permission:quarterly_goal_edit', 'throttle:10,1']);
    Route::get('/quarterly-goals/show/{id}', 'QuarterlyGoalsController@show')->middleware('permission:quarterly_goal_detail');
    Route::post('/quarterly-goals/destroy/{id}', 'QuarterlyGoalsController@destroy')->middleware(['permission:quarterly_goal_delete', 'throttle:10,1']);

    Route::get('/month-goals', 'MonthGoalController@index');
    Route::post('/month-goals/add', 'MonthGoalController@store');
    Route::post('/month-goals/edit/{id}', 'MonthGoalController@update');
    Route::get('/month-goals/show/{id}', 'MonthGoalController@show');
    Route::post('/month-goals/destroy/{id}', 'MonthGoalController@destroy');

    // time sheet
    Route::get('/time-sheet', 'TimeSheetController@index');
    Route::post('/time-sheet/export-excel', 'TimeSheetController@exportExcel')->middleware('throttle:10,1');
    Route::get('/checkin-checkout', 'TimeSheetController@listCheckInCheckOut');
    Route::post('/checkin-checkout-export', 'TimeSheetController@exportCheckInCheckOut');
    Route::get('/time-sheet/{id}', 'TimeSheetController@show');
    Route::get('/explain-miss', 'TimeSheetController@explainMiss');

    Route::get('/checkin-checkout-reject', 'TimeSheetController@listCheckInCheckOutReject');

    // HanetCamUser
    Route::get('/hanet-cam-user', 'HanetCamUserController@index');
    Route::get('/hanet-cam-user-sync', 'HanetCamUserController@syncUsers')->middleware('role:admin|hr');
    Route::post('/hanet-cam-user-update', 'HanetCamUserController@update')->middleware(['role:admin|hr', 'throttle:10,1']);
    Route::post('/hanet-cam-user-remove', 'HanetCamUserController@destroy')->middleware(['role:admin|hr', 'throttle:10,1']);
    Route::post('/hanet-cam-user-register', 'HanetCamUserController@register')->middleware(['role:admin|hr', 'throttle:10,1']);

    // in_outs_online
    Route::get('/in-outs-online', 'InOutsOnlineController@index');
    Route::post('/in-outs-online/add', 'InOutsOnlineController@store');
    Route::post('/in-outs-online/update/{inOutsOnline}', 'InOutsOnlineController@update');
    Route::post('/in-outs-online/destroy/{inOutsOnline}', 'InOutsOnlineController@destroy');
    Route::post('/in-outs-online/approve/{inOutsOnline}', 'InOutsOnlineController@approve')->middleware('permission:in_outs_online_approve');
    Route::post('/in-outs-online/reject/{inOutsOnline}', 'InOutsOnlineController@reject')->middleware('permission:in_outs_online_approve');
    Route::post('/in-outs-online/rollback/{inOutsOnline}', 'InOutsOnlineController@rollback')->middleware('permission:in_outs_online_approve');
    Route::get('/absence-letter-type', 'AbsenceLetterTypeController@index');

    Route::post('/checkin-checkout/explain', 'InOutsOnlineController@checkinCheckoutExplain');
    Route::post('/in-outs-online/HRCreateRecord', 'InOutsOnlineController@HRCreateRecord');
    Route::post('/explanation-hr-create/HRCreateRecord', 'InOutExplanationController@HRCreateRecord');

    // AbsenceYear
    Route::get('/absence-year', 'AbsenceYearController@index');
    Route::post('/absence-year-add', 'AbsenceYearController@addAbsenceYear')->middleware('role:admin|hr_leader|hr');
    Route::post('/absence-year-sync', 'AbsenceYearController@syncStaff')->middleware('role:admin|hr_leader|hr');

    // xin nghỉ phép
    Route::get('/absence-letters', 'AbsenceLetterController@index');
    Route::post('/absence-letters/checkRule', 'AbsenceLetterController@checkRule');
    Route::post('/absence-letters/add', 'AbsenceLetterController@store');
    Route::post('/absence-letters/explain', 'AbsenceLetterController@explain');
    Route::post('/absence-letters/edit/{id}', 'AbsenceLetterController@update');
    Route::get('/absence-letters/show/{id}', 'AbsenceLetterController@show');
    Route::post('/absence-letters/destroy/{id}', 'AbsenceLetterController@destroy');
    Route::post('/absence-letters/approve/{id}', 'AbsenceLetterController@approve')->middleware('permission:absence_letter_approve');
    Route::post('/absence-letters/reject/{id}', 'AbsenceLetterController@reject')->middleware('permission:absence_letter_approve');
    Route::post('/absence-letters/rollback/{id}', 'AbsenceLetterController@rollback')->middleware('permission:absence_letter_approve');
    Route::get('/absence/get-manager/{id}', 'AbsenceLetterController@getManager');
    Route::get('/absence/get-info/{id}', 'AbsenceLetterController@getInfo');
    Route::post('/absence-letters/add-all', 'AbsenceLetterController@addAllAbsence');
    Route::post('/absence-letters/export-excel', 'AbsenceLetterController@exportExcel')->middleware('throttle:10,1');

    // Cấu hình hệ thống
    Route::group(['prefix' => 'system-config'], function () {
        Route::get('/setting-happy-text', 'SettingHappyTextController@index')->middleware('permission:config_system');
        Route::post('/setting-happy-text', 'SettingHappyTextController@store')->middleware(['permission:config_system', 'throttle:10,1']);
        Route::get('/setting-happy-text/{id}', 'SettingHappyTextController@details')->middleware('permission:config_system');
        Route::post('/setting-happy-text/{id}', 'SettingHappyTextController@update')->middleware(['permission:config_system', 'throttle:10,1']);
        Route::get('/setting-happy-sound', 'SettingHappyTextController@sound')->middleware('permission:config_system');
        Route::get('/get-happy-text', 'SettingHappyTextController@getGreetings');
        Route::get('/get-happy-sound', 'SettingHappyTextController@getSound');

        Route::get('/get-email-config', 'SystemConfigController@getEmailConfig')->middleware('permission:config_system');
        Route::get('/get-all-employee', 'SystemConfigController@getEmployees');
        Route::post('/save-email-config', 'SystemConfigController@saveEmailConfig')->middleware(['permission:config_system', 'throttle:10,1']);
        Route::get('/get-kpi-assessment-time-config', 'SystemConfigController@getKpiAssessmentTimeConfig')->middleware(['permission:config_system']);
        Route::post('/save-kpi-assessment-time-config', 'SystemConfigController@saveKpiAssessmentTimeConfig')->middleware(['permission:config_system', 'throttle:10,1']);
        Route::get('/get-explain-config', 'SystemConfigController@getExplainConfig')->middleware(['permission:config_system']);
        Route::post('/save-explain-config', 'SystemConfigController@saveExplainConfig')->middleware(['permission:config_system', 'throttle:10,1']);

        Route::post('/config-happy-birthday', 'HrInfoController@configHappyBirthday');
        Route::get('/get-config-happy-birthday', 'HrInfoController@getConfigHappyBirthday');
        Route::group(['prefix' => 'feedback-object'], function () {
            Route::get('/', 'ConfigFeedbackObjectController@index');
            Route::post('/add', 'ConfigFeedbackObjectController@store')->middleware(['permission:config_system', 'throttle:10,1']);
            Route::post('/{id}/edit', 'ConfigFeedbackObjectController@store')->middleware(['permission:config_system', 'throttle:10,1']);
            Route::get('/{id}/delete', 'ConfigFeedbackObjectController@delete')->middleware('permission:config_system');
        });
    });

    // Quản lý Trường học
    Route::get('/school/{id}/details', 'SchoolController@details');
    Route::get('/school/form-type', 'SchoolController@getSchoolFormType');
    Route::get('/school/type', 'SchoolController@getSchoolType');
    Route::get('/school/level', 'SchoolController@getSchoolLevel');
    Route::get('/school/group-level', 'SchoolController@getSchoolGroupLevel');
    Route::post('/school/create', 'SchoolController@store')->middleware(['permission:school_add', 'throttle:10,1']);
    Route::post('/school/{id}/update', 'SchoolController@store')->middleware(['permission:school_edit', 'throttle:10,1']);
    Route::post('/school/{id}/delete', 'SchoolController@delete')->middleware('permission:school_delete');

    Route::get('/business-trip', 'BusinessTripController@index');
    Route::post('/business-trip/add', 'BusinessTripController@store');
    Route::post('/business-trip/update/{id}', 'BusinessTripController@update');
    Route::post('/business-trip/destroy/{id}', 'BusinessTripController@destroy');
    Route::post('/business-trip/confirm/{id}', 'BusinessTripController@confirm')->middleware('permission:business_trip_approve');

    Route::group(['prefix' => 'hr'], function () {
        Route::post('create-folder', 'FolderController@createFolder');
        Route::get('folder-list', 'FolderController@index');
        Route::get('folder-trash-list', 'FolderController@folderTrashList');
        Route::post('upload-multi-folder', 'FolderController@uploadMultiFolder');
        Route::post('delete-file/{id}', 'FolderController@deleteFile');
        Route::post('delete-folder/{id}', 'FolderController@deleteFolder');
        Route::post('download-file/{id}', 'FolderController@downloadFile');
        Route::post('download-folder/{id}', 'FolderController@downloadFolder');
        Route::get('detail-folder/{id}', 'FolderController@detailFolder');
        Route::post('upload-multi-folder-detail/{id}', 'FolderController@uploadMultiFolderDetail');
        Route::post('restore/{id}', 'FolderController@restore');
        Route::post('share-folder', 'FolderController@shareFolder');

        Route::post('/property/add', 'PropertyController@store');
        Route::post('/property/update/{id}', 'PropertyController@update');
        Route::post('/property/destroy/{id}', 'PropertyController@destroy');
        Route::post('/importExcel/property', 'PropertyController@importExcel');
        Route::get('/property', 'PropertyController@index');
        Route::get('/get-users', 'FolderController@getUsers');
        Route::get('/sub-property', 'SubPropertyController@index');
        Route::post('/sub-property/add', 'SubPropertyController@store');
        Route::post('/sub-property/update/{id}', 'SubPropertyController@update');
        Route::post('/sub-property/destroy/{id}', 'SubPropertyController@destroy');
        Route::get('/sub-property-detail-edit/{id}', 'SubPropertyDetailController@detail');
        Route::get('/sub-property-detail', 'SubPropertyDetailController@list_asset');
        Route::get('/get-all-property', 'PropertyController@getAllProperties');
        Route::get('/get-all-sub-property', 'SubPropertyController@getAllProperties');

        Route::get('/get-all-sub-warranty-history/{id}', 'SubPropertyDetailController@getDataWarrantyHistoty');

        Route::post('/sub-property-detail/add', 'SubPropertyDetailController@store');
        Route::post('/sub-property-detail/update/{id}', 'SubPropertyDetailController@update');
    });


    Route::group(['prefix' => 'kpi'], function () {
        Route::get('/m-kpi', 'MasterKPIController@index');
        Route::post('/m-kpi/add', 'MasterKPIController@store');
        Route::post('/m-kpi/update/{id}', 'MasterKPIController@update');
        Route::post('/m-kpi/destroy/{id}', 'MasterKPIController@destroy');

        Route::get('/kpi-register-list', 'KPIController@index');
        Route::get('/register-kpi/{kpiSummaryId}', 'KPIController@details');
        Route::post('/register-kpi/add', 'KPIController@store');
        Route::post('/register-kpi/update/{kpiSummaryId}', 'KPIController@update');
        Route::post('/register-kpi/destroy/{id}', 'KPIController@destroy');
        Route::post('/change-status-kpi/{id}', 'KPIController@changeStatusKPI');
        Route::post('/leader-confirm-kpi/{kpiSummaryId}', 'KPIController@leaderConfirmKPI');
        Route::post('/leader-reopen-kpi/{kpiSummaryId}', 'KPIController@leaderReopenKPI');
        Route::post('/hr-confirm-kpi/{department_id}', 'KPIController@hrConfirmKPI');
        Route::post('/hr-reopen-kpi/{department_id}', 'KPIController@hrReopenKPI');
        Route::get('/task-management/assign-kpi/{id}', 'KPIController@assignKPI');


        Route::get('/list', 'KPIAssessmentController@index');
        Route::get('/getCriteriaGroup', 'KPIAssessmentController@getCriteriaGroup');
        Route::get('/getCriteriaForAssessment/{kpiSummaryId}', 'KPIAssessmentController@getCriteriaForAssessment');
        Route::get('/getAssessmentSummaryForConfirm/{departmentId}', 'KPIAssessmentController@getAssessmentSummaryForConfirm');
        Route::post('/saveStaffSelfAssessment', 'KPIAssessmentController@saveStaffSelfAssessment')->middleware(['throttle:5,1']);
        Route::post('/saveAssessmentLeader', 'KPIAssessmentController@saveAssessmentLeader')->middleware(['throttle:5,1']);
        Route::post('/leaderReopenStaffSelfAssessment', 'KPIAssessmentController@leaderReopenStaffSelfAssessment')->middleware(['throttle:5,1']);
        Route::post('/assessment-export-excel', 'KPIAssessmentController@exportExcel')->middleware(['throttle:5,1']);

        //upgrade kpi
        Route::get('/upgrade-kpi-register-list', 'KPIUpgradeController@index');
        Route::post('/upgrade-register-kpi/create', 'KPIUpgradeController@store');
        Route::post('/upgrade-register-kpi/update/{kpiSummaryId}', 'KPIUpgradeController@update');
        Route::get('/upgrade-register-kpi/{kpiSummaryId}', 'KPIUpgradeController@detail');
        Route::post('/upgrade-leader-confirm-kpi/{kpiSummaryId}', 'KPIUpgradeController@leaderConfirmKPI');
        Route::post('/upgrade-leader-reopen-kpi/{kpiSummaryId}', 'KPIUpgradeController@leaderReopenKPI');
        Route::get('/upgrade-kpi-assessment-list', 'KPIUpgradeAssessmentController@index');
        Route::get('/upgrade-kpi-assessment/{kpiSummaryId}', 'KPIUpgradeAssessmentController@detail');
        Route::post('/upgrade-saveStaffSelfAssessment', 'KPIUpgradeAssessmentController@saveStaffSelfAssessment')->middleware(['throttle:5,1']);
        Route::post('/upgrade-saveAssessmentLeader', 'KPIUpgradeAssessmentController@saveAssessmentLeader')->middleware(['throttle:5,1']);
        Route::post('/upgrade-leaderReopenStaffSelfAssessment', 'KPIUpgradeAssessmentController@leaderReopenStaffSelfAssessment')->middleware(['throttle:5,1']);
        Route::post('/upgrade-assessment-export-excel', 'KPIUpgradeAssessmentController@exportExcel')->middleware(['throttle:5,1']);
    });

    /** Kinh doanh */
    Route::get('/province-business-market', 'ProvinceBusinessMarketController@index');
    Route::get('/business-area', 'BusinessAreaController@index');
    Route::post('/business-area/add', 'BusinessAreaController@store');
    Route::post('/business-area/{id}/edit', 'BusinessAreaController@edit');
    Route::get('/business-area-all/{id}', 'BusinessAreaController@getAll');

    /** Quản lý đối tác - Đã chuyển sang module PartnerManagement */

    Route::group(['prefix' => 'survey'], function () {
        Route::get('category/get-all', 'SurveyCategoryController@getAll');
        Route::get('category', 'SurveyCategoryController@index')->middleware('permission:survey_category_list');
        Route::get('category/{id}/details', 'SurveyCategoryController@details')->middleware('permission:survey_category_add|survey_category_edit');
        Route::post('category/add', 'SurveyCategoryController@store')->middleware(['permission:survey_category_add', 'throttle:10,1']);
        Route::post('category/{id}/edit', 'SurveyCategoryController@store')->middleware(['permission:survey_category_edit', 'throttle:10,1']);
        Route::post('category/{id}/delete', 'SurveyCategoryController@delete')->middleware('permission:survey_category_delete');

        Route::get('question', 'SurveyQuestionController@index')->middleware('permission:survey_question_list|survey_exam_add|survey_exam_edit');
        Route::get('question/{id}/details', 'SurveyQuestionController@details')->middleware('permission:survey_question_add|survey_question_edit');
        Route::post('question/add', 'SurveyQuestionController@store')->middleware(['permission:survey_question_add', 'throttle:10,1']);
        Route::post('question/{id}/edit', 'SurveyQuestionController@store')->middleware(['permission:survey_question_edit', 'throttle:10,1']);
        Route::post('question/{id}/delete', 'SurveyQuestionController@delete')->middleware('permission:survey_question_delete');

        Route::get('exam', 'SurveyExamController@index')->middleware('permission:survey_exam_list');
        Route::get('exam/{id}/details', 'SurveyExamController@details')->middleware('permission:survey_exam_add|survey_exam_edit');
        Route::post('exam/add', 'SurveyExamController@store')->middleware(['permission:survey_exam_add', 'throttle:10,1']);
        Route::post('exam/{id}/edit', 'SurveyExamController@store')->middleware(['permission:survey_exam_edit', 'throttle:10,1']);
        Route::post('exam/{id}/delete', 'SurveyExamController@delete')->middleware('permission:survey_exam_delete');
        Route::get('exam/participants', 'SurveyExamController@getParticipants')->middleware('permission:survey_exam_add|survey_exam_edit');
        Route::get('exam/check-do-exam', 'SurveyExamController@checkDoExam');
        Route::get('exam/{id}/get-info-to-do-exam', 'SurveyExamController@getInfo2DoExam');
        Route::post('exam/{id}/do-exam', 'SurveyExamController@doExam');
        Route::get('exam/history', 'SurveyExamController@viewHistory');
        Route::get('exam/{examId}/details-history', 'SurveyExamController@detailsHistory');
    });

    Route::group(['prefix' => 'ke'], function () {
        // chức năng gói bán hàng KE
        Route::group(['prefix' => 'sales-packages'], function () {
            Route::get('/', 'KESalesPackagesController@index')->middleware('permission:kidsenglish_sales_packages_list');
            Route::get('{id}/details', 'KESalesPackagesController@details')->middleware('permission:kidsenglish_sales_packages_add|kidsenglish_sales_packages_edit');
            Route::post('add', 'KESalesPackagesController@store')->middleware(['permission:kidsenglish_sales_packages_add', 'throttle:5,1']);
            Route::post('{id}/edit', 'KESalesPackagesController@store')->middleware(['permission:kidsenglish_sales_packages_edit', 'throttle:5,1']);
            Route::post('{id}/delete', 'KESalesPackagesController@delete')->middleware('permission:kidsenglish_sales_packages_delete');
            Route::get('get-equipment-types', 'KESalesPackagesController@getEquipmentTypes');
        });

        // chức năng hợp đồng KE
        Route::group(['prefix' => 'contracts'], function () {
            Route::get('/', 'KEContractController@index')->middleware('permission:contract_list|contract_list_management');
            Route::get('{id}/details', 'KEContractController@details')->middleware('permission:contract_detail|contract_list_management');
            Route::post('add', 'KEContractController@store')->middleware(['permission:contract_add', 'throttle:5,1']);
            Route::post('{id}/edit', 'KEContractController@edit')->middleware(['permission:contract_edit', 'throttle:5,1']);
            Route::post('{id}/delete', 'KEContractController@delete')->middleware(['permission:contract_delete', 'throttle:5,1']);
            Route::get('get-sales-packages', 'KEContractController@getSalesPackages');
            Route::get('get-equipment-types', 'KEContractController@getEquipmentTypes');
            Route::get('destroy-file/{id}', 'KEContractController@destroyFile')->middleware(['permission:contract_edit']);
        });

        // chức năng xuất kho
        Route::group(['prefix' => 'ex-ware-house'], function () {
            Route::get('/', 'KEExWareHouseController@index')->middleware(['permission:kidsenglish_exwarehouse_list']);
            Route::get('get-contract', 'KEExWareHouseController@getContracts')->middleware('permission:kidsenglish_exwarehouse_list|kidsenglish_exwarehouse_add');
            Route::get('get-ware-house', 'KEExWareHouseController@getWareHouses');
            Route::post('/', 'KEExWareHouseController@store')->middleware(['permission:kidsenglish_exwarehouse_add']);
            Route::get('/{id}/details', 'KEExWareHouseController@details')->middleware(['permission:kidsenglish_exwarehouse_list']);
            Route::get('/{id}/delete', 'KEExWareHouseController@delete')->middleware(['permission:kidsenglish_exwarehouse_delete']);
            Route::post('/excel/{id}', 'KEExWareHouseController@exportWarehouse');
            Route::get('/{id}/confirm/{code}', 'KEExWareHouseController@confirmExWarehouse')->middleware(['permission:kidsenglish_exwarehouse_confirm']);
            Route::post('/{id}/reject/{code}', 'KEExWareHouseController@rejectExWarehouse')->middleware(['permission:kidsenglish_exwarehouse_confirm']);
            Route::post('/{id}/comment/{code}', 'KEExWareHouseController@commentExWarehouse')->middleware(['permission:kidsenglish_exwarehouse_list|kidsenglish_exwarehouse_confirm']);
        });

        // Đề xuất tạo tài khoản KE
        Route::group(['prefix' => 'account-allocation'], function () {
            Route::get('/', 'KEAccountAllocationController@index')->middleware('permission:kidsenglish_account_allocation_list');
            // Route::get('{id}/details', 'KESalesPackagesController@details')->middleware('permission:kidsenglish_sales_packages_add|kidsenglish_sales_packages_edit');
            // Route::post('add', 'KESalesPackagesController@store')->middleware(['permission:kidsenglish_sales_packages_add', 'throttle:5,1']);
            // Route::post('{id}/edit', 'KESalesPackagesController@store')->middleware(['permission:kidsenglish_sales_packages_edit', 'throttle:5,1']);
            // Route::post('{id}/delete', 'KESalesPackagesController@delete')->middleware('permission:kidsenglish_sales_packages_delete');
            // Route::get('get-equipment-types', 'KESalesPackagesController@getEquipmentTypes');
        });
    });

    Route::post('notification/store-device-id', 'PushNotificationController@storeDeviceId');

    // giải trình đi muộn về sớm
    Route::group(['prefix' => 'in-out'], function () {
        Route::post('/explanation', 'InOutExplanationController@store');
        Route::post('/explanation-confirm', 'InOutExplanationController@confirm');
        Route::post('/explanation-comment', 'InOutExplanationCommentController@store');
        Route::post('/explanation-confirm-miss', 'InOutExplanationController@confirmMiss');
    });
    // changelog
    Route::get('/changelog/categories', 'ChangeLogController@getCategories');
    Route::get('/changelog', 'ChangeLogController@index');
    Route::get('/changelog/show/{id}', 'ChangeLogController@detail');
    Route::post('/changelog/add', 'ChangeLogController@store');
    Route::post('/changelog/{id}/edit', 'ChangeLogController@update');
    Route::post('/changelog/destroy/{id}', 'ChangeLogController@destroy');

    //Đăng ký công tác
    Route::group(['prefix' => 'business'], function () {
        Route::post('/business-plan/create', 'BusinessPlanController@create');
        Route::post('/business-plan/edit/{id}', 'BusinessPlanController@edit');
        Route::get('/business-plan-list', 'BusinessPlanController@list');
        Route::get('/getVehicle', 'BusinessPlanController@getVehicle');
        Route::get('/business-plan-show/{id}', 'BusinessPlanController@show');
        Route::post('/business-plan/approve/{id}', 'BusinessPlanController@approve');
        Route::post('/business-plan/reject/{id}', 'BusinessPlanController@reject');
        Route::post('/business-report/update/{id}', 'BusinessReportController@update');
        Route::get('/business-report-list', 'BusinessReportController@list');
        Route::get('/business-report-show/{id}', 'BusinessReportController@show');
        Route::post('/business-report/approve/{id}', 'BusinessReportController@approve');
        Route::post('/business-report/reject/{id}', 'BusinessReportController@reject');
        Route::post('/business-comment/{id}', 'BusinessReportController@comment');
        Route::get('/comment/list/{id}', 'BusinessReportController@commentList');
        Route::post('/business-plan/delete/{id}', 'BusinessPlanController@delete');
        Route::post('/export-request-advance/{id}', 'BusinessPlanController@exportRequestAdvance');
        Route::post('/export-request-complete/{id}', 'BusinessReportController@exportRequestComplete');
    });

    Route::group(['prefix' => 'app-display'], function () {
        Route::get('/screen-category', 'AppDisplayController@index');
        Route::post('/update/{id}', 'AppDisplayController@update');
        Route::get('/show/{id}', 'AppDisplayController@show');
    });

    Route::group(['prefix' => 'frequently-question'], function () {
        Route::get('/list', 'FrequentlyQuestionController@list');
        Route::post('/create', 'FrequentlyQuestionController@create');
        Route::post('/update/{id}', 'FrequentlyQuestionController@update');
        Route::get('/show/{id}', 'FrequentlyQuestionController@show');
        Route::post('/delete/{id}', 'FrequentlyQuestionController@delete');
        Route::post('/download-file/{id}', 'FrequentlyQuestionController@downloadFile');
    });
});

Route::group([
    'prefix' => 'call-center',
    'middleware' => 'api.allowIpAddress',
    'namespace' => 'App\Http\Controllers',
], function () {
    // call center request
    Route::get('/tickets', 'Tasks\TicketController@list');
    Route::post('/ticket/create-ticket', 'Tasks\TicketController@store');
    Route::post('/ticket/update-ticket/{id}', 'Tasks\TicketController@update');
    Route::get('/ticket/show/{id}', 'Tasks\TicketController@show');
    Route::post('/ticket/destroy/{id}', 'Tasks\TicketController@destroy');
    Route::post('/ticket/addComment/{id}', 'Tasks\TicketController@addComment');
    Route::post('/ticket-destroy-file/{id}', 'Tasks\TicketController@destroyFile');
    Route::get('/products', 'ProductController@index');
    Route::get('/departments', 'DepartmentController@index');
    Route::get('/clients-by-product', 'ClientContactController@index');
    Route::get('/{clientId}/phone-numbers', 'ClientContactController@phoneNumberByClient');
    Route::post('/add-phone-number-to-client', 'ClientContactController@addPhoneNumberToClient');
    Route::get('/client-info-by-phone', 'ClientContactController@getClientInfoByPhone');
});

// api đồng bộ role và permission cho toàn bộ người dùng
// comment lại nhưng không được xóa api này, api lúc cần đồng bộ user và role sẽ bật lên chạy ở local 1 lần là được
// Route::get('/syn-roles', [RoleController::class, 'syn_role']);

// API test send mail bằng jobs
// Route::get('/test-send-mail', 'App\Http\Controllers\MailController@test');
Route::get('/test-send-event', 'App\Http\Controllers\TestController@testSendEvent');


// Huynn viet api cho app
Route::group([
    'prefix' => 'mobile',
    'namespace' => 'App\Http\Controllers'
], function ($router) {
    Route::post('/login', 'AuthController@loginMobile');
    Route::post('/reset-password', 'ResetPasswordController@sendMail');
    Route::post('/refresh-token', 'AuthController@refreshTokenMobile');
    Route::get('/app-version', 'AppVersionController@getAppVersion');

    Route::group([
        'middleware' => 'api.mobile.auth',
    ], function () {
        // User
        Route::post('/change-pass', 'AuthController@changePassWordMobile');
        Route::get('/user-profile', 'AuthController@userProfileMobile');
        Route::get('/update-profile', 'AuthController@updateProfileMobile');
        Route::post('/update-firebase-token', 'AuthController@updateFirebaseToken');
        Route::post('/logout', 'AuthController@logoutMobile');
        Route::post('/avatar/uploadImage', 'UserController@mobileUploadImage');

        // Notification
        Route::get('/notification', 'NotificationController@listNotificationMobile');
        Route::get('/notification-index', 'NotificationController@listAllMobile');
        Route::get('/notification-detail', 'NotificationController@notiDetailMobile');
        Route::post('/notification/markAsRead', 'NotificationController@markAsReadMobile')->middleware('throttle:10,1');
        Route::post('/notification/markAsReadAll', 'NotificationController@markAsReadAllMobile')->middleware('throttle:10,1');
        Route::post('/notification/markUnRead', 'NotificationController@markUnReadMobile')->middleware('throttle:10,1');

        // xin nghỉ phép
        Route::get('/absence-letters', 'AbsenceLetterController@listAbsenceLetter');
        Route::get('/absence/get-manager', 'AbsenceLetterController@getManagerMobile');
        Route::post('/absence-letters/destroy', 'AbsenceLetterController@destroyMobile');

        Route::get('/absence-letters/view-add', 'AbsenceLetterController@viewAddMobile');
        Route::get('/absence-letters/view-update', 'AbsenceLetterController@viewUpdateMobile');
        Route::get('/absence-letters/view-approve', 'AbsenceLetterController@viewApprove');

        Route::post('/absence-letters/add', 'AbsenceLetterController@addMobile');
        Route::post('/absence-letters/edit', 'AbsenceLetterController@updateMobile');

        Route::post('/absence-letters/approve', 'AbsenceLetterController@approveMobile')->middleware('permission:absence_letter_approve');
        Route::post('/absence-letters/reject', 'AbsenceLetterController@rejectMobile')->middleware('permission:absence_letter_approve');
        Route::post('/absence-letters/rollback', 'AbsenceLetterController@rollbackMobile')->middleware('permission:absence_letter_approve');

        // phong ban
        Route::get('/departments', 'DepartmentController@listDepartmentMobile');
        Route::get('/departments-user', 'DepartmentController@userDepartmentMobile');

        // Cấp bậc
        Route::get('/positions', 'DepartmentController@userPositionMobile');

        // Chechin-checkout Online
        Route::get('/in-outs-online', 'InOutsOnlineController@listMobile');
        Route::get('/in-outs-online/view-add', 'InOutsOnlineController@viewAddMobile');
        Route::post('/in-outs-online/add', 'InOutsOnlineController@addMobile');
        Route::post('/in-outs-online/update', 'InOutsOnlineController@updateMobile');
        Route::post('/in-outs-online/destroy', 'InOutsOnlineController@destroyMobile');
        Route::post('/in-outs-online/approve', 'InOutsOnlineController@approveMobile')->middleware('permission:in_outs_online_approve');
        Route::post('/in-outs-online/reject', 'InOutsOnlineController@rejectMobile')->middleware('permission:in_outs_online_approve');
        Route::post('/in-outs-online/rollback', 'InOutsOnlineController@rollbackMobile')->middleware('permission:in_outs_online_approve');

        // Đăng ký công tác
        Route::get('/business-trip', 'BusinessTripController@indexMobile');
        Route::get('/business-trip/view-add', 'BusinessTripController@viewAddMobile');
        Route::get('/business-trip/view-update', 'BusinessTripController@viewUpdateMobile');
        Route::post('/business-trip/add', 'BusinessTripController@storeMobile');
        Route::post('/business-trip/update', 'BusinessTripController@updateMobile');
        Route::post('/business-trip/destroy', 'BusinessTripController@destroyMobile');
        Route::post('/business-trip/reject', 'BusinessTripController@rejectMobile')->middleware('permission:business_trip_approve');
        Route::post('/business-trip/confirm', 'BusinessTripController@confirmMobile')->middleware('permission:business_trip_approve');

        // AbsenceYear
        Route::get('/absence-year', 'AbsenceYearController@indexMobile');
        Route::get('/absence-year/view-history', 'AbsenceYearController@viewHistoryMobile');
        Route::post('/absence-year-add', 'AbsenceYearController@addAbsenceYearMobile')->middleware('role:admin|hr_leader|hr');
        Route::post('/absence-year-sync', 'AbsenceYearController@syncStaffMobile')->middleware('role:admin|hr_leader|hr');

        // Check in Check out
        Route::get('/checkin-checkout', 'TimeSheetController@listCheckInCheckOutMobile');
        Route::post('/checkin-checkout/explain', 'InOutsOnlineController@checkinCheckoutExplainMobile');

        //đăng ký kpi
        Route::get('/upgrade-kpi-register-list', 'KPIUpgradeController@indexMobile'); //lấy list đăng ký kpi
        Route::post('/upgrade-register-kpi/create', 'KPIUpgradeController@storeMobile'); //lưu đăng ký kpi
        Route::post('/upgrade-register-kpi/update', 'KPIUpgradeController@updateMobile'); //chỉnh sửa đăng ký kpi
        Route::get('/upgrade-register-kpi', 'KPIUpgradeController@detailMobile'); //shơ chi tiết đăng ký kpi
        Route::post('/upgrade-leader-confirm-kpi', 'KPIUpgradeController@leaderConfirmKPIMobile'); //leader xác nhận đăng ký kpi
        Route::post('/upgrade-leader-reopen-kpi', 'KPIUpgradeController@leaderReopenKPIMobile'); // leader mở lại đăng ký kpi

        // check in checkout
        Route::post('/checkin-checkout/add-comment', 'InOutExplanationCommentController@addCommentMobile');
        Route::post('/checkin-checkout/approve', 'InOutExplanationController@approveMobile')->middleware('permission:in_out_explanation_approve');
        Route::post('/checkin-checkout/explain/add', 'InOutExplanationController@addOrEditExplainMobile');
        Route::post('/checkin-checkout/explain/rollback', 'InOutExplanationController@rollbackExplainMobile')->middleware('permission:in_out_explanation_approve');
        Route::post('/checkin-checkout/explain/reject', 'InOutExplanationController@rejectExplainMobile')->middleware('permission:in_out_explanation_approve');
        # Đánh giá kpi
        Route::get('/upgrade-kpi-assessment-list', 'KPIUpgradeAssessmentController@listMobile');
        Route::get('/upgrade-kpi-assessment-detail', 'KPIUpgradeAssessmentController@detailMobile');
        Route::post('/upgrade-saveStaffSelfAssessment', 'KPIUpgradeAssessmentController@saveStaffSelfAssessmentMobile')->middleware(['throttle:5,1']);
        Route::post('/upgrade-saveAssessmentLeader', 'KPIUpgradeAssessmentController@saveAssessmentLeaderMobile')->middleware(['throttle:5,1']);
        Route::post('/upgrade-leaderReopenStaffSelfAssessment', 'KPIUpgradeAssessmentController@leaderReopenStaffSelfAssessmentMobile')->middleware(['throttle:5,1']);
        # chức năng khách hàng
        Route::group(['prefix' => 'clients'], function () {
            Route::get('/', 'ClientController@indexMobile')->middleware('permission:client_list|client_list_management');
        });
        // Contract
        Route::get('/master/contract', 'ContractController@indexMobile')->middleware('permission:contract_list|contract_list_management');
        Route::get('/contract/show', 'ContractController@showMobile')->middleware(['permission:contract_detail|contract_list_management']);
        Route::post('/contract/edit', 'ContractController@updateMobile')->middleware(['permission:contract_edit', 'throttle:10,1']);
        Route::get('/contract/payment', 'ContractController@paymentListMobile')->middleware('permission:contract_add|import_excel_contract');
        Route::post('/contract/payment-save', 'ContractController@paymentSaveMobile')->middleware('permission:contract_add|import_excel_contract');
        Route::post('/contract/destroy', 'ContractController@destroyMobile')->middleware(['permission:contract_delete', 'throttle:10,1']);
        Route::post('/contract/review-reject/{importContractId}', 'ImportContractController@rejectTmpContractBaseMobile')->middleware(['permission:review_contract']);

        // Tmp_contract
        Route::get('/get-review-contract-form-list', 'ContractController@contractFormListMobile')->middleware('permission:contract_add|review_contract');
        Route::get('/get-tmp-contract-base-detail', 'ImportContractController@getTmpContractBaseDetailMobile')->middleware(['permission:show_import_contract_list|review_contract|import_excel_contract']);
        Route::post('/edit-tmp-contract-base-form', 'ContractController@editTmpContractMobile');
        Route::post('/master/contract/add', 'ContractController@storeMobile')->middleware(['permission:contract_add', 'throttle:10,1']);
        Route::post('/delete-tmp-contract-form', 'ContractController@deleteContractFormMobile')->middleware('permission:contract_add');
        Route::post('/approve-tmp-contract-form', 'ContractController@approveContractFormMobile')->middleware('permission:review_contract');
        Route::post('/reject-tmp-contract-form', 'ContractController@rejectContractFormMobile')->middleware('permission:review_contract');

        // Filter
        Route::get('/master/contract/all-filter', 'ContractController@allFilterMobile')->middleware('permission:contract_list|contract_list_management|client_list|client_list_management');

        Route::get('/time-sheet', 'TimeSheetController@indexMobile');
        Route::get('/explain-miss', 'TimeSheetController@explainMissMobile');

        // bussiness trip plane
        Route::group(['prefix' => 'business-plane'], function () {
            Route::get('/staff-vehicle', 'BusinessPlanController@getStaffAndVehiclesApi');
            Route::get('/list', 'BusinessPlanController@listBusinessPlanApi');
            Route::post('/create', 'BusinessPlanController@createMobile');
            Route::post('/edit', 'BusinessPlanController@editMobile');
            Route::get('/detail', 'BusinessPlanController@detailMobile');
            Route::post('/approve', 'BusinessPlanController@approveMobile');
            Route::post('/reject', 'BusinessPlanController@rejectMobile');
            Route::post('/delete', 'BusinessPlanController@deleteBusinessPlan');
        });
        Route::group(['prefix' => 'business-plane-report'], function () {
            Route::get('/list', 'BusinessReportController@businessReportsMobile');
            Route::get('/detail', 'BusinessReportController@showMobile');
            Route::post('/comment', 'BusinessReportController@addCommentMobile');
            Route::post('/edit', 'BusinessReportController@editBusinessReportMobile');
            Route::post('/rank', 'BusinessReportController@rankBusinessReportMobile');
        });
        Route::get('/v2/functions-active', 'AppDisplayController@showMobile');
        Route::get('/functions-active', 'FunctionsManagerController@getFunctionsActiveMobile');
    });
});


Route::group(['prefix' => 'integration'], function () {
    Route::post('/user/send-notification', [IntegrationUserController::class, 'sendNotification'])->name('integration.user.send-notification');
});

<?php

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Broadcast;
use Tymon\JWTAuth\Contracts\Providers\Auth as ProvidersAuth;



/*
|--------------------------------------------------------------------------
| Broadcast Channels
|--------------------------------------------------------------------------
|
| Here you may register all of the event broadcasting channels that your
| application supports. The given channel authorization callbacks are
| used to check if an authenticated user can listen to the channel.
|
*/

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('vietec-message-{id}', function ($user, $id) {
    return md5($user->id) == $id;
});
Broadcast::channel('2vietec-message-{id}', function ($user, $id) {
    return md5($user->id) == $id;
});

Broadcast::channel('private-partner-{id}', function ($partner, $id) {
    //return true;
    return md5($partner->id) == $id;
});

Broadcast::channel('Modules.PartnerManagement.Models.Partner.{id}', function ($partner, $id) {
    //return true;
    return (int) $partner->id === (int) $id;
});
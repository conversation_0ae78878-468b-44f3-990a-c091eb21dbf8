<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        $this->call(DegreeSeeder::class);
        $this->call(ProductSeeder::class);
        $this->call(EmploymentContractSeeder::class);
        $this->call(PermissionSeeder::class);
        $this->call(ModuleSeeder::class);
        $this->call(UserSeeder::class);
        $this->call(UserWorkingSeeder::class);
        $this->call(AbsenceLetterTypeSeeder::class);
        $this->call(\Modules\PartnerManagement\Database\Seeders\PartnerPermissionSeeder::class);
        $this->call(PartnerRoleSeeder::class);
    }
}

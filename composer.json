{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "anik/amqp": "^2.3", "anik/laravel-amqp": "^1.4", "berkayk/onesignal-laravel": "^1.0", "doctrine/dbal": "^3.4", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "itsgoingd/clockwork": "^5.1", "jenssegers/agent": "^2.6", "jenssegers/mongodb": "^3.8", "kreait/laravel-firebase": "*", "laravel-notification-channels/fcm": "^2.7", "laravel/framework": "^8.75", "laravel/sanctum": "^2.11", "laravel/tinker": "^2.5", "league/fractal": "^0.20.1", "phpoffice/phpspreadsheet": "^1.23", "pusher/pusher-php-server": "^7.2", "spatie/laravel-activitylog": "^4.10", "spatie/laravel-fractal": "^6.3", "spatie/laravel-permission": "^5.5", "tymon/jwt-auth": "^1.0.2"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^5.10", "phpunit/phpunit": "^9.5.10"}, "autoload": {"files": ["app/helpers.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "modules/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "minimum-stability": "dev", "prefer-stable": true}
import Vue from "vue";
import OneSignalVue from "onesignal-vue";

import App from "./App.vue";
import router from "./router";
import store from "./store";
import { initialize } from "./helpers/general";
import "./helpers/filters";
import VueTour from "vue-tour";

require("vue-tour/dist/vue-tour.css");
Vue.use(VueTour);
window._ = require("lodash");

//bootstrap vue
import { BootstrapVue } from "bootstrap-vue";
import "bootstrap-vue/dist/bootstrap-vue.css";
import "@fortawesome/fontawesome-free/css/all.min.css";
Vue.use(BootstrapVue);
Vue.use(OneSignalVue);
//perfect scrollbar
import PerfectScrollbar from "vue2-perfect-scrollbar";
Vue.use(PerfectScrollbar);

//vue-scrollactive
import VueScrollactive from "vue-scrollactive";
Vue.use(VueScrollactive);

//vue-meta
import VueMeta from "vue-meta";
Vue.use(VueMeta, {
    refreshOnceOnNavigation: true,
});

//Sweetalert
import VueSweetalert2 from "vue-sweetalert2";
import "sweetalert2/dist/sweetalert2.min.css";
const options = {
    confirmButtonColor: "#4361ee",
    cancelButtonColor: "#e7515a",
};
Vue.use(VueSweetalert2, options);

//portal vue
import PortalVue from "portal-vue";
Vue.use(PortalVue);

//vue-i18n
import i18n from "./i18n";

Vue.config.productionTip = false;

// Vue VSwitch
import VSwitch from "v-switch-case";

Vue.use(VSwitch);

// set default settings
import appSetting from "./app-setting";
Vue.prototype.$appSetting = appSetting;
appSetting.init();

initialize(store, router);

import Echo from "laravel-echo";
import Pusher from "pusher-js";

window.Pusher = Pusher;


import $ from "jquery";
window.$ = window.jQuery = $;

new Vue({
    router,
    store,
    i18n,
    render: (h) => h(App),
    beforeMount() {
        // this.$OneSignal.init({
        //     appId: process.env.MIX_ONESIGNAL_APP_ID,
        //     allowLocalhostAsSecureOrigin: false,
        // }).catch(e => {
        //     console.log("onesignal initial error", e.message)
        // });
    },
    created() {},
}).$mount("#app");

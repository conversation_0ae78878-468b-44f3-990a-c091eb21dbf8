<template>
    <div :class="[$store.state.layout_style, $store.state.menu_style]">
        <component v-bind:is="layout"></component>
    </div>
</template>
<script>
// layouts

import appLayout from "./layouts/app-layout.vue";
import authLayout from "./layouts/auth-layout.vue";
import partnerLayout from "./layouts/partner-layout.vue";

import "@/assets/sass/app.scss";

export default {
    metaInfo: {
        title: "Vietec",
        titleTemplate: "%s | Vietec - Tiếp nối thành công",
    },
    components: {
        app: appLayout,
        auth: authLayout,
        partner: partnerLayout,
    },
    computed: {
        layout() {
            return this.$store.getters.layout;
        },
    },
    data() {
        return {};
    },
    mounted() {},
    methods: {},
};
</script>

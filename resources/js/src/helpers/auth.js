import axios from 'axios';
import c from './common';

export function login(credential) {
    return new Promise((res, rej) => {
        axios.post('/api/auth/login', credential)
            .then(result => {
                res(result.data);
            })
            .catch(err => {
                rej("Wrong email or password!");
            })
    })
}

export function register(credential) {
    return new Promise((res, rej) => {
        axios.post('/api/auth/register', credential)
            .then(result => {
                res(result.data);
            })
            .catch(err => {
                rej("Wrong email or password!");
            })
    })
}

export function currentUser() {
    const user = localStorage.getItem('user');

    if (!user) {
        return null;
    }

    return JSON.parse(user);
}

export function logout() {
    let token = c.token();
    axios.defaults.headers.common['Authorization'] = "bearer " + token;
    axios.post('/api/auth/logout')
        .then(response => {
            
        })
        .catch(e => {
            c.lg(e, 'catch');
        });
}

// Partner
export function partner_login(credential) {
    return new Promise((res, rej) => {
        axios.post('/api/partners/auth/login', credential)
            .then(result => {
                res(result.data);
            })
            .catch(err => {
                rej("Tài khoản không hợp lệ.");
            })
    })
}

export function partner_logout() {
    let token = c.token();
    axios.defaults.headers.common['Authorization'] = "bearer " + token;
    axios.post('/api/partners/auth/logout')
        .then(response => {
            
        })
        .catch(e => {
            c.lg(e, 'catch');
        });
}
import Vue from 'vue'
import moment from 'moment'

Vue.filter('strToLowerCase', (v) => v ? v & v.toString().toLowerCase() : '')
Vue.filter('strToUpperCase', (v) => v ? v.toString().toUpperCase() : '')
Vue.filter('genderToName', (v) => v && v.toString().toLowerCase() === '1' ? 'Nam' : 'Nữ')
Vue.filter('parentInfo', (name, phone) => name && phone ? `${name} (${phone})` : name)
Vue.filter('prepareText', (v, l = 20) => v && v.length ? u.sub(v, l) : '')
Vue.filter('filterPercentage', (v, p) => parseInt(p) > 0 ? `${+(`${Math.round(`${(parseInt(v) / parseInt(p)) * 100}e+2`)}e-2`)}%` : '0%')
Vue.filter('filterRatio', (v, p) => parseInt(p) > 0 ? +(`${Math.round(`${parseInt(v) / parseInt(p)}e+2`)}e-2`) : '0')
Vue.filter('validSessionContractType', (t, v) => t === 0 ? 3 : v)
Vue.filter('countLearnDay', (v) => Math.ceil(v))
Vue.filter('dayLeft', (v) => Math.floor(v))
Vue.filter('formatNumber', (v) => !isNaN(v) && v > 0 ? parseInt(v).toFixed(1).replace(/(\d)(?=(\d{3})+\.)/g, '$1,').slice(0, -2) : 0)
Vue.filter('formatNumber3', (v) => !isNaN(v) ? parseInt(v).toFixed(1).replace(/(\d)(?=(\d{3})+\.)/g, '$1,').slice(0, -2) : 0)
Vue.filter('formatNumber2', (v) => parseFloat(v).toFixed(2))
Vue.filter('formatMoney', (v, c = ' đ') => !isNaN(v) && v > 0 && c !== '' ? `${parseInt(v).toFixed(1).replace(/(\d)(?=(\d{3})+\.)/g, '$1,').slice(0, -2)}${c}` : `0${c}`)
Vue.filter('formatMoney1', (v, c = ' đ') => !isNaN(v) && v > 0 && c !== '' ? `${parseInt(v).toFixed(1).replace(/(\d)(?=(\d{3})+\.)/g, '$1,').slice(0, -2)}${c}` : ``)
Vue.filter('formatMoney2', (v, c = 'đ') => !isNaN(v) && v != 0 && v != null && c !== '' ? `${parseInt(v).toFixed(1).replace(/(\d)(?=(\d{3})+\.)/g, '$1,').slice(0, -2)}${c}` : `0${c}`)
Vue.filter('trimChar', (v) => v.rtrim())
Vue.filter('totalPerMax', (total, max) => max ? `${parseInt(total)}/${parseInt(max)}` : '')
Vue.filter('tuitionFeeLabel', (name, price) => name && price ? `${name} - ${price}` : name)
Vue.filter('parentLabel', (name, phone) => name && phone ? `${name} (${phone})` : name)
Vue.filter('shortText', (v, l = 20) => v.toString().length && !isNaN(l) && l > 10 ? u.sub(v, l) : '')
Vue.filter('workingDate', (v, d = '') => v && v !== 'null' && v !== '0000-00-00' && v !== '' ? moment(v).format('YYYY-MM-DD') : d)
Vue.filter('formatDate', (v) => v && v !== '0000-00-00' && v !== '0000-00-00 00:00:00' ? moment(v).format('YYYY-MM-DD') : v)
Vue.filter('formatDate2', (v) => v && v !== '0000-00-00' && v !== '0000-00-00 00:00:00' ? moment(v).format('DD-MM-YYYY') : v)
Vue.filter('formatDate3', (v) => v && v !== '0000-00-00' && v !== '0000-00-00 00:00:00' ? moment(v).format('DD-MM-YYYY HH:mm:ss') : v)
Vue.filter('formatDate4', (v) => v && v !== '0000-00-00' && v !== '0000-00-00 00:00:00' ? moment(v).format('DD/MM/YYYY HH:mm:ss') : '')
Vue.filter('formatDate5', (v) => v && v !== '0000-00-00' && v !== '0000-00-00 00:00:00' ? moment(v).format('DD/MM/YYYY') : v)
Vue.filter('formatTime', (v) => v ? moment(v).format('YYYY-MM-DD HH:mm:ss') : v)
Vue.filter('timeFormat', (v, f = 'YYYY-MM-DD HH:mm:ss') => v ? moment(v).format(f) : v)
Vue.filter('timeShiftFormat', (v, f = 'HH:mm:ss') => v ? moment(v).format(f) : v)
Vue.filter('timeFromNow', (v, f = 'YYYY-MM-DD HH:mm:ss') => v ? moment(v)
    .locale("vi")
    .fromNow() : v)
Vue.filter('displayClass', (n, s) => parseInt(s, 10) === 0 ? '' : n)
Vue.filter('formatAnswer', (is_right) => is_right ? 'fa-check' : '')
Vue.filter('formatCurrency', (v, c = 'đ') => {
    let resp = ''
    let number = 0
    let currency = c
    if (parseFloat(v) < 100) {
        number = parseFloat(v)
        currency = '%'
        resp = `${number}${currency}`
    } else {
        number = parseInt(v)
        resp = number > 0 ? `${number.toFixed(1).replace(/(\d)(?=(\d{3})+\.)/g, '$1,').slice(0, -2)}${currency}` : 0
    }
    return resp
})
Vue.filter('formatCurrency2', (v, c = 'đ') => {
    let resp = ''
    let number = 0
    let currency = c
    if (parseFloat(v) < 100) {
        number = parseFloat(v)
        currency = 'đ'
        resp = `${number}${currency}`
    } else {
        number = parseInt(v)
        resp = number > 0 ? `${number.toFixed(1).replace(/(\d)(?=(\d{3})+\.)/g, '$1,').slice(0, -2)}${currency}` : 0
    }
    return resp
})
Vue.filter('formatIndexDate', (index) => {
    let resp = '';
    index = parseInt(index);
    switch (index) {
        case 1:
            resp = 'T2';
            break;
        case 2:
            resp = 'T3';
            break;
        case 3:
            resp = 'T4';
            break;
        case 4:
            resp = 'T5';
            break;
        case 5:
            resp = 'T6';
            break;
        case 6:
            resp = 'T7';
            break;
        case 7:
            resp = 'CN';
            break;
    }
    return resp
})

Vue.filter('kpiStatus', (status) => {
    switch (status) {
        case 1:
            return 'Đang chờ leader duyệt';
        case 2:
            return "Leader xác nhận ĐK KPI";
        case 3:
            return "Leader mở lại ĐK KPI";
        case 4:
            return "Nhân viên TĐG KPI";
        case 5:
            return "Leader đánh giá KPI ";
        case 6:
            return "Leader mở lại TĐG KPI";
        default:
            return "";
    }

})

Vue.filter('exportContractStatus', (index) => {
    let resp = '';
    index = parseInt(index);
    switch (index) {
        case 1:
            resp = 'Chưa yêu cầu xuất HĐ';
            break;
        case 2:
            resp = "Đang yêu cầu xuất HĐ";
            break;
        case 3:
            resp = "Đã xuất HĐ";
            break;
        case 4:
            resp = "Mở lại cập nhật HĐ";
            break;
    }
    return resp
})

Vue.filter('company', (index) => {
    let resp = '';
    index = parseInt(index);
    switch (index) {
        case 1:
            resp = 'Vietec';
            break;
        case 2:
            resp = "Gokids";
            break;
        case 3:
            resp = "MetaKids";
            break;
    }
    return resp
})

Vue.filter('changeLogStatus', (status) => {
    switch (status) {
        case 0:
            return 'Lưu nháp';
        case 1:
            return "Công bố";
        default:
            return "";
    }

})

Vue.filter('status', (status) => {
    switch (status) {
        case 1:
            return 'NV đăng ký CT';
        case 2:
            return "TP duyệt đăng ký CT";
        case 3:
            return "TP từ chối đăng ký CT";
        case 4:
            return "NV cập nhật báo cáo CT";
        case 5:
            return "TP duyệt báo cáo CT";
        case 6:
            return "TP từ chối báo cáo CT";
        default:
            return "";
    }

})

Vue.filter('rank', (rank) => {
    switch (rank) {
        case 1:
            return 'Tốt';
        case 2:
            return "Khá";
        case 3:
            return "Trung bình";
        default:
            return "";
    }
})
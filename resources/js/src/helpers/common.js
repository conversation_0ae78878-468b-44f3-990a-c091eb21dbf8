import Vue from "vue";
import Axios from "axios";
import VueRouter from "vue-router";
import moment from "moment";
import md5 from "js-md5";
import { isUndefined, isNullOrUndefined, isString } from "util";
import store from "@/store";
import checkPermission from "./permission";
import storage from "./storage";

const vietec = new Vue();

let temp = {
    calling: {},
};

const lg = (val, lbl = "Data") => {
    console.log(
        "\n%c" + prl("=") + "\n" + lbl + "\n" + prl("-"),
        "color:Green"
    );
    console.log(JSON.parse(JSON.stringify(val)));
    console.log("%c" + prl("=") + "\n", "color:Green");
};

const log = (val, lbl = "Data") => {
    console.log(
        "\n%c" + prl("=") + "\n" + lbl + "\n" + prl("-"),
        "color:Green"
    );
    console.log(val);
    console.log("%c" + prl("=") + "\n", "color:Green");
};

function prl(c, n) {
    const l = n > 0 ? n : 150;
    let p = c || "-";
    let r = "";
    for (let i = 0; i < l; i++) {
        r += p;
    }
    return r;
}

const isNumber = (variable) => {
    return typeof variable === "number";
};

const isValidDate = (date) => {
    return moment(date).isValid();
};

// Synchronous version for backward compatibility
const token = () => {
    try {
        // Try to get from localStorage first for backward compatibility
        const userStr = localStorage.getItem("user");
        if (userStr) {
            const user = JSON.parse(userStr);
            return user && user.token ? user.token : null;
        }
        return null;
    } catch (error) {
        console.error("Error getting token:", error);
        return null;
    }
};

// Synchronous version for backward compatibility
const session = () => {
    try {
        // Try to get from localStorage first for backward compatibility
        return {
            user: JSON.parse(localStorage.getItem("user") || "null"),
            infos: JSON.parse(localStorage.getItem("infos") || "null"),
            me: JSON.parse(localStorage.getItem("me") || "null"),
            account_type: "app",
        };
    } catch (error) {
        console.error("Error getting session:", error);
        return { account_type: "app" };
    }
};

// Synchronous version for backward compatibility
const partner_token = () => {
    try {
        // Try to get from localStorage first for backward compatibility
        const userStr = localStorage.getItem("partner");
        if (userStr) {
            const user = JSON.parse(userStr);
            return user && user.token ? user.token : null;
        }
        return null;
    } catch (error) {
        console.error("Error getting partner token:", error);
        return null;
    }
};

// Synchronous version for backward compatibility
const partner_session = () => {
    try {
        // Try to get from localStorage first for backward compatibility
        return {
            partner: JSON.parse(localStorage.getItem("partner") || "null"),
            categories: JSON.parse(
                localStorage.getItem("categories") || "null"
            ),
            me: JSON.parse(localStorage.getItem("me") || "null"),
            account_type: "partner",
        };
    } catch (error) {
        console.error("Error getting partner session:", error);
        return { account_type: "partner" };
    }
};

function live(variable) {
    return typeof variable !== "undefined";
}

const is = {
    in: (obj, key) =>
        obj && Array.isArray(obj) && key
            ? parseInt(obj.indexOf(key), 10) > -1
            : false,
    obj: (obj) => typeof obj === "object" && !Array.isArray(obj),
    arr: (obj) => Array.isArray(obj),
    has: (obj, key) =>
        typeof obj === "object" && !Array.isArray(obj)
            ? Object.hasOwnProperty.call(obj, key)
            : false,
    for: (obj) => Object.keys(obj),
    json: (o) =>
        typeof o === "string" &&
        (typeof jsp(o) === "object" || Array.isArray(jsp(o))),
};

const a = (unauthorize) => {
    const check =
        isNullOrUndefined(unauthorize) ||
        unauthorize === "" ||
        parseInt(unauthorize) === 0;
    if (check) {
        // Try to get from localStorage first for backward compatibility
        let account_type = localStorage.getItem("account_type");
        let tokenKey = undefined;
        if (account_type === "partner") tokenKey = partner_token();
        if (account_type === "app") tokenKey = token();

        if (tokenKey)
            Axios.defaults.headers.common["Authorization"] =
                "bearer " + tokenKey;
    } else {
        if (is.has(Axios.defaults.headers.common, "Authorization"))
            delete Axios.defaults.headers.common["Authorization"];
        if (unauthorize === 2)
            Axios.defaults.headers.post["Content-Type"] =
                "application/x-www-form-urlencoded";
    }

    // Axios.defaults.baseURL = APP_URL trong env
    return Axios;
};

const g = (link, attributes = null, unauthorize = false, blob = false) =>
    new Promise((resolve, reject) => {
        if (typeof link === "string") {
            let hash = "";
            if (!unauthorize) {
                hash = md5(`${link}${moment().format("YYYY-MM-DD HH:ii:ss")}`);
            }
            if (
                !live(temp.calling) ||
                (live(temp.calling) && !is.has(temp.calling, hash))
            ) {
                if (hash) {
                    temp.calling[hash] = "processing...";
                }
                const payload = {
                    params: attributes,
                };
                if (blob) {
                    payload.responseType = "blob";
                }
                a()
                    .get(link, payload)
                    .then((response) => {
                        let result = response.data;
                        if (
                            !unauthorize &&
                            live(temp.calling) &&
                            is.has(temp.calling, hash)
                        ) {
                            delete temp.calling[hash];
                        }
                        if (!blob && result.code && result.code == 401) {
                            if (!store.getters.IS_EXPIRED) {
                                alert(
                                    `Không thể truy xuất được dữ liệu từ máy chủ vì:\n\n*${result.error}*`
                                );
                                store.commit("setIsExpired", true);
                            }
                            localStorage.removeItem("user");
                            location.reload();
                        } else if (!blob && result.code && result.code == 403) {
                            vietec.$swal({
                                icon: "error",
                                title: "Bạn không có quyền thao tác chức năng này!",
                                confirmButtonText: "ok",
                                showLoaderOnConfirm: true,
                                closeOnClickOutside: false,
                                closeOnEsc: false,
                                allowOutsideClick: false,
                                preConfirm: () => {
                                    window.location.href = "/";
                                },
                            });
                        } else if (!blob && result.code && result.code == 500) {
                            alert(
                                `Không thể truy xuất được dữ liệu từ máy chủ vì lỗi:\n\n*${result.message}*`
                            );
                            reject(result);
                        } else if (
                            !blob &&
                            result.code &&
                            result.code !== 200
                        ) {
                            resolve(result);
                        } else {
                            if (blob) {
                                resolve(result);
                            } else {
                                resolve(result.data ? result.data : result);
                            }
                        }
                    })
                    .catch((e) => {
                        // lg(e, 'catch get common js')
                        store.commit("toggleLoading", false);
                        if (e.response && e.response.status === 403) {
                            vietec.$swal({
                                icon: "error",
                                title: "Bạn không có quyền thao tác chức năng này!",
                                confirmButtonText: "ok",
                                showLoaderOnConfirm: true,
                                closeOnClickOutside: false,
                                closeOnEsc: false,
                                allowOutsideClick: false,
                                preConfirm: () => {
                                    window.location.href = "/";
                                },
                            });
                        } else if (e.response && e.response.status === 500) {
                            store.commit("setSnackBar", {
                                type: "danger",
                                msg: "Hệ thống đang bận. Vui lòng quay lại sau!",
                            });
                            reject(e);
                        } else {
                            let msg =
                                e.response.data.message ||
                                "Có lỗi trong quá trình xử lý!";
                            if (
                                e.response.data.errors &&
                                Object.values(e.response.data.errors).length
                            ) {
                                msg = Object.values(e.response.data.errors)[0];
                            }

                            store.commit("setSnackBar", {
                                type: "danger",
                                msg,
                            });

                            e.response.status !== 400 && reject(e);
                        }
                    });
            } else {
                lg(temp.calling[hash], "Double request!");
                reject("Double request...!");
            }
        } else {
            reject("Request url is not valid!");
        }
    });

const p = (
    link,
    params = null,
    unauthorize = false,
    full_response = false,
    headers = null
) =>
    new Promise((resolve, reject) => {
        if (typeof link === "string") {
            let hash = "";
            if (!unauthorize) {
                hash = md5(
                    `${link}${JSON.stringify(params)}${moment().format(
                        "YYYY-MM-DD HH:ii:ss"
                    )}`
                );
            }
            if (
                !live(temp.calling) ||
                (live(temp.calling) && !is.has(temp.calling, hash))
            ) {
                if (hash) {
                    temp.calling[hash] = "processing...";
                }
                const config = {};
                if (headers) {
                    config.headers = headers;
                }

                a()
                    .post(link, params, config)
                    .then((response) => {
                        if (!full_response) {
                            const result = response.data;
                            if (result.code && result.code == 401) {
                                if (!store.getters.IS_EXPIRED) {
                                    alert(
                                        `Không thể truy xuất được dữ liệu từ máy chủ vì:\n\n*${result.error}*`
                                    );
                                    store.commit("setIsExpired", true);
                                }
                                // Remove from both localStorage and IndexedDB
                                localStorage.removeItem("user");
                                storage.removeItem("user").catch(console.error);
                                location.reload();
                            } else if (result.code && result.code == 500) {
                                vietec.$swal({
                                    icon: "error",
                                    text:
                                        "Có lỗi trong quá trình xử lý, " +
                                        result.message,
                                    confirmButtonText: "ok",
                                    showLoaderOnConfirm: true,
                                    closeOnClickOutside: false,
                                    closeOnEsc: false,
                                    allowOutsideClick: false,
                                });
                                reject(result);
                            } else if (result.code && result.code == 888) {
                                vietec.$swal({
                                    icon: "error",
                                    title: result.message,
                                    confirmButtonText: "ok",
                                    showLoaderOnConfirm: true,
                                    closeOnClickOutside: false,
                                    closeOnEsc: false,
                                    allowOutsideClick: false,
                                    preConfirm: () => {
                                        window.location.href = "/";
                                    },
                                });
                            } else if (result.code && result.code !== 200) {
                                resolve(result);
                            } else {
                                resolve(result.data ? result.data : result);
                            }
                        } else {
                            resolve(response);
                        }
                    })
                    .catch((e) => {
                        // lg(e, 'catch post common js')
                        store.commit("toggleLoading", false);
                        if (e.response && e.response.status === 403) {
                            vietec.$swal({
                                icon: "error",
                                title: "Bạn không có quyền thao tác chức năng này!",
                                confirmButtonText: "ok",
                                showLoaderOnConfirm: true,
                                closeOnClickOutside: false,
                                closeOnEsc: false,
                                allowOutsideClick: false,
                                preConfirm: () => {
                                    window.location.href = "/";
                                },
                            });
                        } else if (e.response && e.response.status === 500) {
                            vietec.$swal({
                                icon: "error",
                                title: "Hệ thống đang bận. Vui lòng quay lại sau!",
                                confirmButtonText: "ok",
                                showLoaderOnConfirm: true,
                                closeOnClickOutside: false,
                                closeOnEsc: false,
                                allowOutsideClick: false,
                            });
                            reject(e);
                        } else {
                            let msg =
                                e.response.data.message ||
                                "Có lỗi trong quá trình xử lý!";
                            if (
                                e.response.data.errors &&
                                Object.values(e.response.data.errors).length
                            ) {
                                msg = "";
                                Object.values(e.response.data.errors).forEach(error => {
                                    msg += "✗ " + error + "\n<br>";
                                });
                                msg = "<strong>"+msg.trim()+"</strong>";
                            }

                            
                            // store.commit("setSnackBar", {
                            //     type: "danger",
                            //     msg,
                            // });
                            vietec.$swal({
                                icon: "error",
                                html: msg,
                                confirmButtonText: "Chấp nhận",
                                showLoaderOnConfirm: true,
                                closeOnClickOutside: false,
                                closeOnEsc: false,
                                allowOutsideClick: false,
                            });

                            e.response.status !== 400 && reject(e);
                        }
                    });
            } else {
                log("Double request", temp.calling[hash]);
                reject("Double request...!");
            }
        } else {
            reject("Request url is not valid!");
        }
    });

// export excel method post
const ep = (link, params = null) =>
    new Promise((resolve, reject) => {
        if (typeof link === "string") {
            const tokenKey = token();
            Axios.post(link, params, {
                headers: {
                    Authorization: "bearer " + tokenKey,
                },
                responseType: "blob",
            })
                .then((response) => {
                    resolve(response.data);
                })
                .catch((e) => {
                    reject(e);
                });
        } else {
            reject("Request url is not valid");
        }
    });

function isGreaterThan(_from, _to) {
    let _from_time = new Date(_from); // Y-m-d
    let _to_time = new Date(_to); // Y-m-d
    return _from_time.getTime() > _to_time.getTime() ? true : false;
}

function getDayName(dayOfWeek) {
    let weekday = [
        "Sunday",
        "Monday",
        "Tuesday",
        "Wednesday",
        "Thursday",
        "Friday",
        "Saturday",
    ];
    return !isNaN(dayOfWeek) && dayOfWeek >= 0 && dayOfWeek <= 6
        ? weekday[parseInt(dayOfWeek)]
        : "";
}

const go = (obj, route) => {
    if (obj && route) {
        obj.push(route);
    } else return false;
};

const formatMoney = (input) =>
    input.toFixed(2).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,");

const formatMoney2 = (input) =>
    input.toFixed(0).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,");

const formatNumber = (input) => parseInt(input.replace(/,/g, ""));

const formatDate = (input) =>
    input && input != "0000-00-00" ? moment(input).format("YYYY-MM-DD") : input;

const formatDate1 = (input) =>
    input && input != "0000-00-00" ? moment(input).format("DD-MM-YYYY") : input;

const formatDate2 = (input) =>
    input && input != "0000-00-00"
        ? moment(input).format("DD-MM-YYYY HH:mm:ss")
        : input;

function fmc(input) {
    let code = "";
    let drap = null;
    let resp = {
        s: "",
        n: 0,
    };
    if (!input || input.toString() === "" || input.toString() === "0") {
        resp.n = 0;
        resp.s = "0";
    } else {
        drap = input.toString().replace(/[\D\s\._\-]+/g, "");
        drap = drap ? parseInt(drap, 10) : 0;
        resp.n = drap;
        resp.s = drap === 0 ? "0" : `${drap.toLocaleString("en-US")}`;
    }
    return resp;
}

const months = [
    "Tháng 1",
    "Tháng 2",
    "Tháng 3",
    "Tháng 4",
    "Tháng 5",
    "Tháng 6",
    "Tháng 7",
    "Tháng 8",
    "Tháng 9",
    "Tháng 10",
    "Tháng 11",
    "Tháng 12",
];

const color_charts = [
    "#1b55e2",
    "#e7515a",
    "#1abc9c",
    "#e2a03f",
    "#805dca",
    "#3b3f5c",
    "#795548",
    "#ffeb3b",
];

function getErrorMessage(exception) {
    let msg = "";
    let errors = exception.response.data.errors;
    for (let key in errors) {
        msg += errors[key];
    }
    return msg;
}

const getProvincesByProducts = (productIds) => {
    if (!Array.isArray(productIds)) {
        productIds = [productIds];
    }

    const { provinces } = session().infos;
    if (
        !checkPermission(["client_list_management"]) &&
        !checkPermission(["contract_list_management"]) &&
        checkPermission([
            "product_owner_config_range",
            "client_list",
            "client_add",
            "client_edit",
            "client_delete",
            "client_detail",
            "contract_list",
            "contract_add",
            "contract_edit",
            "contract_delete",
            "contract_detail",
        ])
    ) {
        const rs = [];
        for (const item of provinces) {
            if (
                item.sale_areas.some((x) => productIds.includes(x.product_id))
            ) {
                rs.push({
                    province_id: item.province_id,
                    name: item.name,
                    districts: item.districts
                        .filter((x) =>
                            x.sale_areas?.some((s) =>
                                productIds.includes(s.product_id)
                            )
                        )
                        .map((x) => ({
                            district_id: x.district_id,
                            name: x.name,
                        })),
                });
            } else {
                continue;
            }
        }
        return rs;
    }
    return provinces;
};

function getRoleName() {
    try {
        // Try to get from localStorage first for backward compatibility
        const user = JSON.parse(localStorage.getItem("user"));
        return user && user.role ? user.role[0] : null;
    } catch (error) {
        console.error("Error getting role name:", error);
        return null;
    }
}

function isLeader() {
    try {
        // Try to get from localStorage first for backward compatibility
        const me = JSON.parse(localStorage.getItem("me"));
        return me && me.position && me.position.is_leader;
    } catch (error) {
        console.error("Error checking if user is leader:", error);
        return false;
    }
}

function isAdmin() {
    let role = getRoleName();
    return role == "admin";
}

function isHr() {
    let role = getRoleName();
    let arrRole = ["hr", "hr_leader"];
    return arrRole.includes(role);
}

function isManeger() {
    let role = getRoleName();
    let arrRole = ["hr", "hr_leader", "admin", "ceo"];
    return arrRole.includes(role);
}

function formatDateToDDMMYYYY(dateStr) {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    if (isNaN(date)) return "";
    const dd = String(date.getDate()).padStart(2, "0");
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const yyyy = date.getFullYear();
    return `${dd}/${mm}/${yyyy}`;
}

const KPI_STATUS = {
    NOT_REGISTER: 0,
    STAFF_REGISTER_KPI: 1,
    LEADER_CONFIRM_KPI: 2,
    LEADER_REOPEN_KPI: 3,
    HR_REOPEN_KPI: 4,
    HR_CONFIRM_KPI: 5,
};

const KPI_STATUS_TEXT = {
    NOT_REGISTER: "Chưa đăng ký KPI",
    STAFF_REGISTER_KPI: "Đang chờ leader duyệt",
    LEADER_CONFIRM_KPI: "Leader xác nhận đăng ký KPI",
    LEADER_REOPEN_KPI: "Leader mở lại đăng ký KPI",
    HR_REOPEN_KPI: "HR mở lại đăng ký KPI",
    HR_CONFIRM_KPI: "HR xác nhận đăng ký KPI",
};

export default {
    lg,
    log,
    isNumber,
    isValidDate,
    token,
    session,
    live,
    is,
    md5,
    a,
    g,
    p,
    isGreaterThan,
    getDayName,
    go,
    formatMoney,
    formatMoney2,
    formatNumber,
    ep,
    fmc,
    vietec,
    months,
    color_charts,
    getErrorMessage,
    getProvincesByProducts,
    formatDate,
    formatDate1,
    formatDate2,
    getRoleName,
    isLeader,
    KPI_STATUS,
    KPI_STATUS_TEXT,
    isAdmin,
    isHr,
    isManeger,
    partner_token,
    partner_session,
    formatDateToDDMMYYYY,
};

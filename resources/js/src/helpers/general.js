import axios from "axios";

export function initialize(store, router) {
    // console.log("init....");
    // console.log(router.currentRoute);

    router.beforeEach((to, from, next) => {
        // mạc định sẽ dùng layout app
        //store.commit("setLayout", "app");
        store.commit(
            "toggleMenuStyle",
            to.meta && to.meta.menuStyle ? to.meta.menuStyle : "vertical"
        );
        if (
            to.meta &&
            to.meta.layout &&
            ["app","auth", "partner"].includes(to.meta.layout)
        ) {
            store.commit("setLayout", to.meta.layout);
        }

        const requiresAuth = to.matched.some(
            (record) => record.meta.requiresAuth
        );
        let currentUser = null;
        if (to.meta.layout == "app") {
            currentUser = store.state.Auth.currentUser;
        }
        if (to.meta.layout == "partner") {
            currentUser = store.state.PartnerAuth ? store.state.PartnerAuth.currentUser : null;
        }
        //console.log("Required="+requiresAuth+", Current User=", currentUser);
        if (requiresAuth && (currentUser === null || currentUser === undefined)) {
            store.commit("setLayout", "auth");
            next(
                to.path.startsWith("/partners/") ? "/partners/login" : "/login"
            );
        } else if (to.path === "/login" && currentUser) {
            next("/");
        } else {
            next(true);
        }
    });

    // axios.interceptors.response.use(null, (error) => {
    //     if(error.response.status === 401) {
    //         store.commit('LOGOUT');
    //         router.push('/login');
    //     }

    //     return Promise.reject(error);
    // });

    // if (store.getters.currentUser) {
    //     axios.defaults.headers.common['Authorization'] = `Bearer ${store.getters.CURRENT_USER.token}`;
    // }
}

table#user_contract>tbody>tr>td {
    white-space: pre;
}

.th {
    min-width: 200px;
}

.th-50 {
    min-width: 50px;
}

.th-100 {
    min-width: 100px;
}

.th-120 {
    min-width: 120px;
}

.th-130 {
    min-width: 130px;
}

.th-150 {
    min-width: 150px;
}

.th-200 {
    min-width: 200px;
}

.th-250 {
    min-width: 250px;
}

.th-300 {
    min-width: 300px;
}

.th-350 {
    min-width: 350px;
}

.th-400 {
    min-width: 400px;
}

.th-450 {
    min-width: 450px;
}

.th-500 {
    min-width: 500px;
}

.th-school-name {
    min-width: 300px;
}

.toolbar-search {
    width: 100%;
}

.size-14 {
    width: 14px !important;
    height: 14px !important;
    vertical-align: middle !important;
}

.size-16 {
    width: 16px !important;
    height: 16px !important;
    vertical-align: middle !important;
}

.size-18 {
    width: 18px !important;
    height: 18px !important;
    vertical-align: top !important;
}

.size-20 {
    width: 20px !important;
    height: 20px !important;
    vertical-align: middle !important;
}

.pb-20 {
    padding-bottom: 20px;
}

.card-footer-cus {
    padding: 0.75rem 1.25rem !important;
}

.card-body-cus {
    padding: 0.75rem 1.25rem;
}

.collapse-search {
    padding: 10px;
}

.card-body {
    padding: 5px !important;
}

.group-button {
    float: right;
    display: flex;
}

.button-reset {
    margin-right: 10px;
}

.default-color {
    background-color: #d3d3d3 !important;
    border-color: #d3d3d3;
    box-shadow: 0 10px 20px -10px #d3d3d3;
    color: #555555 !important;
}

.default-color:hover {
    border-color: #d3d3d3;
    color: #555555 !important;
}

.default-color:focus {
    border-color: #d3d3d3;
    color: #555555 !important;
}

.default-color:not(:disabled):not(.disabled):active {
    border-color: #d3d3d3;
    color: #555555 !important;
}

.p-20 {
    padding: 20px;
}

.mrt-20 {
    margin-top: 20px;
}

.w-33 {
    width: 33.333%;
}

.bt {
    border-bottom: 1px solid #dadce0;
}

.w-70 {
    width: 70%;
}

.w-30 {
    width: 30%;
}

.w-15 {
    width: 15%;
}

.flex {
    display: flex;
}

.bor {
    border: 1px solid #dadce0;
}

.bor-rad-8 {
    border-radius: 8px;
}

.w-40 {
    width: 40%;
}

.w-60 {
    width: 60%;
}

.p-15 {
    padding: 15px;
}

.w-65 {
    width: 65%;
}

.w-6 {
    width: 6%;
}

.w-35 {
    width: 35%;
}

.w-5 {
    width: 5%;
}

.fs-20 {
    font-size: 20px;
}

.cl-title {
    color: #3b3f5c;
}

.text-tran-upper {
    text-transform: uppercase;
}

.mrl-20 {
    margin-left: 20px;
}

.mrl-2rem {
    margin-left: 2rem;
}

.height-100 {
    min-height: 100%;
}

#fileInput {
    display: none;
}

.fr {
    float: right;
}

.avatar {
    width: 200px;
    height: 220px;
}

.mrt-15 {
    margin-top: 15px;
}

.mt--15 {
    margin-top: -15px !important;
}

.content-card {
    border: 1px solid #00000020;
    border-radius: 4px;
    padding: 15px;
}

.mrb-15 {
    margin-bottom: 15px;
}

.show-password {
    position: relative;
    right: 35px;
    top: 9px;
    cursor: pointer;
}

.fw {
    font-weight: bold;
}

.cursor {
    cursor: pointer;
}

.mr-right-5 {
    margin-right: 5px !important;
}

.icon-remove {
    margin: auto;
    color: red;
    border: 1px solid;
    border-radius: 13px;
}

.icon-remove:hover {
    color: red;
}

.mrb-10 {
    margin-bottom: 10px;
}

.mrt-10 {
    margin-top: 10px;
}

.text-left {
    text-align: left;
}

.yellow {
    color: #ffbb44;
}

.mr-b-5 {
    margin-bottom: 0.5rem !important;
}

.primary {
    color: #4361ee !important;
}

.a:hover {
    cursor: pointer;
}

.p-9 {
    padding: 9px;
}

.header-province {
    background: #eff5ff;
    color: #515365;
    font-weight: 600;
    font-size: 14px;
}

.text-center {
    text-align: center;
}

.mr-15 {
    margin: 15px;
}

.blue {
    color: blue;
}

.mrt-1 {
    margin-top: 1rem;
}

.ml-05 {
    margin-left: 0.5rem;
}

.mr-05 {
    margin-right: 0.5rem;
}

.p-1rem {
    padding: 1rem;
}

.h-10 {
    height: 10%;
}

.h-60 {
    height: 60%;
}

.h-30 {
    height: 30%;
}

.pt-15 {
    padding-top: 1.5rem;
}

.pb-05 {
    padding-bottom: 0.5rem;
}

.title-total {
    color: red !important;
}

.h-100vh {
    height: max-content;
    min-height: 100vh;
}

.h-60vh {
    height: 60vh !important;
}

.h-38vh {
    height: 38vh !important;
}

.h-75vh {
    height: 75vh;
}

.test-justify {
    text-align: justify;
}

.navbar .navbar-item .nav-item.user-profile-dropdown .dropdown-menu {
    max-width: 15rem !important;
}

.icon-comment {
    margin-top: 0.35rem;
    margin-left: 0.5rem;
    font-size: x-large;
}

.product-card {
    min-width: 14rem;
    min-height: 12rem;
    box-shadow: 2px 5px 17px 0 rgb(31 45 61 / 17%);
    background: #fff;
    border-radius: 10px;
    padding: 30px;
    cursor: pointer;
}

.product-card .card-user_name {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #515365;
    min-height: 19.19px;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-card p {
    color: #515365;
    font-size: 14px;
    letter-spacing: 1px;
    font-weight: 500;
    line-height: 23px;
}

.text-two-lines-ellipsis {
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.project-name {
    min-height: 46px;
}

.user-info {
    display: flex;
    padding: 22px 0 0 0;
}

.user-info .avatar {
    margin-right: 15px;
    width: 48px;
    height: 48px;
}

.media-body h5 {
    font-size: 13px;
    font-weight: 600;
    color: #888ea8;
    -webkit-line-clamp: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.user-info .card-user_occupation {
    color: #888ea8;
    font-size: 13px;
}

.s-heading {
    text-transform: uppercase;
    color: #515365;
    font-size: 16px;
    font-weight: 600;
}

.task-text {
    flex-grow: 1;
    padding: 15px 15px 0;
}

.scrumboard .task-header h4 {
    font-size: 14px !important;
}

.scrumboard .card.task-text-progress .card-body .task-content p {
    padding: 5px 15px !important;
}

.scrumboard .card .card-body .task-body .task-bottom {
    padding: 0 15px 15px !important;
}

.task-id h4 {
    font-size: 16px;
    font-weight: 600;
    color: #888ea8;
}

.task-assign {
    width: 32px;
    margin: 15px 15px 0 0;
}

.task-assign img {
    min-width: 32px;
    width: 32px;
    height: 32px;
    border-radius: 50% !important;
}

.task-bottom i {
    font-size: 16px;
}

.custom-table tr {
    cursor: pointer;
}

.scrumboard .card .card-body .task-body .task-bottom div.tb-section-1 span {
    font-weight: 500 !important;
}

.preview-attachment-img {
    min-width: 150px;
    max-width: 200px;
    max-height: 150px;
    object-fit: contain;
}

#addTaskModal .timeline-simple .timeline-list .timeline-post-content div.user-profile img {
    width: 32px;
    height: 32px;
    margin-right: 15px;
}

#addTaskModal .timeline-simple .timeline-list .timeline-post-content div.user-profile img::after {
    top: 32px;
}

.filtered-list-search-live .form-inline button {
    border-radius: 50%;
    padding: 7px 7px;
    position: absolute;
    right: 20px !important;
    top: 4px;
}

.view-more-assign-toggle {
    margin-top: 2px;
}

.view-more-assign-toggle span {
    display: inline-block;
    margin-left: -8px;
    font-size: 16.8px;
}

.form-row .b-avatar-text,
.form-row .more-name {
    font-size: calc(12.8px) !important;
    cursor: pointer;
}

.view-more-assign .dropdown-menu {
    max-height: 400px;
    overflow-y: auto;
    min-width: max-content;
}

.b-avatar-group .b-avatar.assign-active {
    transform: translateY(-5px) scale(1.02);
    border-color: #007bff !important;
}

.text-medium-priority {
    color: #f0ad4e !important;
}

.filtered-list-search-live .form-inline button.custom-search-btn {
    right: 10px !important;
}

.input-group .input-group-append .input-group-text svg {
    color: #4361ee !important;
    cursor: pointer !important;
}

.input-group-append .custom-select {
    border-radius: 0 0.25rem 0.25rem 0 !important;
}

.vue-tags-input {
    max-width: inherit !important;
}

.ti-input {
    border: 1px solid #bfc9d4 !important;
    color: #3b3f5c !important;
    font-size: 15px !important;
    letter-spacing: 1px !important;
    height: auto;
    min-height: calc(1.4em + 1.4rem + 2px) !important;
    border-radius: 6px !important;
    width: 100% !important;
    font-weight: 400 !important;
    line-height: 1.5 !important;
    background-clip: padding-box !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#ke_ex_ware_house_detail_modal input[disabled] {
    color: #3b3f5c !important;
}

#ke_ex_ware_house_detail_modal .multiselect__select {
    background: none !important;
}

#ke_ex_ware_house_detail_modal .multiselect__select::before {
    content: none !important;
}

#ke_ex_ware_house_detail_modal .multiselect.multiselect--disabled {
    cursor: not-allowed !important;
    opacity: 1;
    background: none;
}

#ke_ex_ware_house_detail_modal .multiselect__tags {
    background-color: #f1f2f3 !important;
    border: 1px solid #bfc9d4 !important;
    border-radius: 6px !important;
    cursor: not-allowed !important;
    height: calc(1.4em + 1.4rem + 4px);
}

.payment-history-image {
    width: 100px;
    height: 100px;
    object-fit: contain;
}

th>div {
    white-space: pre-line;
}

.multiselect__tags {
    border: 1px solid #bfc9d4 !important;
    padding: 11px 40px 3px 8px !important;
}

.multiselect__placeholder {
    color: #35495e !important;
    margin-bottom: 8px !important;
    padding-left: 5px !important;
    line-height: 20px !important;
    font-size: 16px !important;
    padding-top: unset !important;
}

.btn-sm-pd {
    padding: 2px 4px !important;
}

.form-control.flatpickr.flatpickr-input[readonly] {
    background-color: #fff !important;
}

table tr td a i.far {
    cursor: pointer;
}

table tr td a i.far:hover {
    background-color: #e7515a;
    color: #fff;
}

h6.item-notify {
    font-weight: 400 !important;
}
p.item-notify-date {
    font-weight: 400 !important;
    font-size: 10px !important;
}

.navbar .navbar-item .nav-item.dropdown.notification-dropdown .nav-link span.badge {
    top: -8px !important;
    right: -7px !important;
    width: 20px !important;
    height: 20px !important;
    background: #ff0000 !important;
    padding-top: 3px !important;
}

.navbar .navbar-item .nav-item.dropdown.notification-dropdown .dropdown-menu .dropdown-item:hover .data-info h6.text-danger {
    color: #445ede !important;
}

.navbar .navbar-item .nav-item.dropdown.notification-dropdown .dropdown-menu {
    min-width: 20rem !important;
    max-height: 450px !important;
    overflow-x: auto;
}

.folder-hr {
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 5px;
}

.folder-name {
    padding-left: 5px;
}

.empty-icon {
    width: 10rem;
}

.w-100 {
    width: 100%;
}

.w-10 {
    width: 10%;
}

.mw-10 {
    max-width: 10%;
}

.w-20 {
    width: 20%;
}

.w-65 {
    width: 65% !important;
}

.w-35 {
    width: 35% !important;
}

.color-header-tb {
    color: #515365;
}

.bg-header-tb {
    background: #eff5ff;
}

.bt:last-child {
    border-bottom: none;
}

.mr-auto {
    margin: auto;
}

.many-columns .custom-table .table td,
.many-columns .custom-table .table th,
.many-columns th>div,
.many-columns .table>tbody>tr>td,
.many-columns .badge,
.many-columns input {
    vertical-align: middle;
    white-space: nowrap;
    font-size: 11px !important;
}

.ti-new-tag-input.ti-valid {
    font-size: 15px !important;
}

.pre-line {
    white-space: pre-line !important;
}

.w-40px {
    width: 40px;
}

.w-50px {
    width: 50px;
}

.w-100px {
    width: 100px;
}

.w-150px {
    width: 150px;
}

.w-200px {
    width: 200px;
}

.component-card_1 {
    border: 1px solid #e0e6ed;
    border-radius: 6px;
    box-shadow: 4px 6px 10px -3px #bfc9d4;
    padding: 20px;
}

.mrb-0 {
    margin-bottom: 0px;
}

.fa-question-circle {
    color: #506690;
    margin-right: 5px;
    vertical-align: middle;
    width: 20px;
    height: 20px;
    stroke-width: 1.6;
    font-size: 20px;
}

.feather-x-circle {
    color: rgb(255, 0, 0);
}
/* 
===============
    Sidebar
===============
*/

.sidebar-wrapper {
    width: 212px;
    position: fixed;
    z-index: 1028;
    border-radius: 6px 6px 0 0;
    transition: 0.6s;
    height: 100vh;
    touch-action: none;
    user-select: none;
    -webkit-user-drag: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    top: 106px;
    left: 16px;
}

.sidebar-theme {
    background: transparent;
}

.sidebar-closed {
    &.sbar-open {
        padding: 0px;
    }

    > {
        .sidebar-wrapper {
            left: -270px;
        }

        #content {
            margin-left: 0;
        }
    }
}

#sidebar {
    .navbar-brand .img-fluid {
        display: inline;
        width: 44px;
        height: auto;
        margin-left: 20px;
        margin-top: 5px;
    }

    .border-underline {
        border-left: 1px solid #ccc;
        height: 20px;
        margin-top: 18px;
        margin-left: 0px;
        margin-right: 8px;
    }

    * {
        overflow: hidden;
        white-space: nowrap;
    }
}

.shadow-bottom {
    display: block;
    position: absolute;
    z-index: 2;
    height: 33px;
    width: 100%;
    pointer-events: none;
    margin-top: -13px;
    left: -4px;
    -webkit-filter: blur(5px);
    filter: blur(3px);
    background: -webkit-linear-gradient(180deg, #f1f2f3 49%, #f1f2f3f2 85%, #2c303c00);
    background: linear-gradient(#f2f4f4 41%, rgba(255, 255, 255, 0.11) 95%, rgba(255, 255, 255, 0));
}

#sidebar {
    ul.menu-categories {
        position: relative;
        margin: auto;
        width: 100%;
        height: calc(100vh - 127px) !important;
        overflow: hidden;

        &.ps {
            height: calc(100vh - 107px) !important;
            margin-right: 0;
            padding-right: 22px;
            border-right: 1px solid #e0e6ed;
        }
    }

    .ps__thumb-y {
        right: 4px;
    }

    ul.menu-categories {
        li {
            > .dropdown-toggle {
                &[data-active='true'] svg.feather-chevron-right,
                &[aria-expanded='true'] svg.feather-chevron-right {
                    transform: rotate(90deg);
                }
            }

            &.menu {
                &:first-child {
                    ul.submenu > li a {
                        justify-content: flex-start;

                        i {
                            align-self: center;
                            margin-right: 12px;
                            font-size: 19px;
                            width: 21px;
                        }
                    }

                    > .dropdown-toggle {
                        margin-top: 21px;
                    }
                }

                > .dropdown-toggle {
                    display: flex;
                    justify-content: space-between;
                    cursor: pointer;
                    font-size: 13px;
                    color: #0e1726;
                    padding: 11px 14px;
                    transition: 0.6s;
                    margin-bottom: 4px;
                    font-weight: 600;
                    letter-spacing: 1px;

                    > div {
                        align-self: center;
                    }
                }

                .dropdown-toggle:after {
                    display: none;
                }

                > {
                    .dropdown-toggle {
                        svg {
                            color: #506690;
                            margin-right: 14px;
                            vertical-align: middle;
                            width: 20px;
                            height: 20px;
                            stroke-width: 1.6;
                        }

                        &[data-active='true'] {
                            background: #fff !important;
                            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                            border-radius: 6px;
                            color: #0e1726;

                            svg {
                                color: #030305;
                                fill: #e0e6ed;
                            }

                            &:hover svg,
                            span {
                                color: #030305;
                            }
                        }

                        &:hover,
                        &.dropdown-toggle:not([data-active='true']):not([aria-expanded='true']):hover {
                            background: #bfc9d4;
                            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                            border-radius: 6px;
                        }

                        &[aria-expanded='true']:not([data-active='true']) {
                            background: #bfc9d4;
                            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                            border-radius: 6px;
                            color: #0e1726;
                        }

                        &:hover svg {
                            color: #030305;
                        }

                        &[aria-expanded='true']:not([data-active='true']) svg {
                            color: #030305;
                            color: #506690;
                        }

                        svg.feather-chevron-right {
                            vertical-align: middle;
                            margin-right: 0;
                            width: 15px;
                        }

                        &[data-active='true'] svg {
                            &.flaticon-right-arrow,
                            &.flaticon-down-arrow {
                                background-color: transparent;
                            }
                        }
                    }

                    a span:not(.badge) {
                        vertical-align: middle;
                    }
                }

                &.active > a {
                    background: #fff;
                    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
                    border-radius: 6px;
                    color: #0e1726;

                    svg {
                        color: #030305;
                        fill: #e0e6ed;
                    }

                    &:hover svg,
                    span {
                        color: #030305;
                    }
                }
            }
        }

        ul.submenu > li a {
            position: relative;
            display: flex;
            justify-content: space-between;
            padding: 10px 12px 10px 48px;
            padding-left: 24px;
            margin-left: 0px;
            font-size: 13px;
            color: #515365;
        }

        li.menu ul.submenu > li a:before {
            content: '';
            background-color: #d3d3d3;
            position: absolute;
            height: 3px;
            width: 3px;
            top: 17.5px;
            left: 13px;
            border-radius: 50%;
        }

        ul.submenu > li {
            &.active a {
                color: #4361ee;
                border-top-left-radius: 10px;
                border-bottom-left-radius: 10px;
                font-weight: 600;
            }

            a.dropdown-toggle {
                padding: 10px 15px 10px 24px;
            }
        }

        li.menu ul.submenu > li.active a:before {
            background-color: #4361ee;
        }

        ul.submenu {
            > li {
                &.active {
                    position: relative;
                }

                a {
                    &:hover {
                        color: #4361ee;

                        &:before {
                            background-color: #4361ee !important;
                        }
                    }

                    i {
                        align-self: center;
                        font-size: 9px;
                    }
                }
            }

            li > {
                [data-active='true'] {
                    i {
                        color: #4361ee;
                    }

                    &:before {
                        background-color: #4361ee !important;
                    }
                }

                a[data-active='true'] {
                    color: #4361ee;
                }
            }

            > li ul.sub-submenu > li {
                a {
                    position: relative;
                    padding: 10px 12px 10px 48px;
                    padding-left: 13px;
                    margin-left: 47px;
                    font-size: 12px;
                    color: #515365;
                }

                &.active a {
                    color: #805dca;
                }

                a {
                    &:hover {
                        color: #4361ee;
                    }

                    &:before {
                        display: none;
                    }
                }

                &.active a:before {
                    background-color: #4361ee;
                }
            }
        }
    }
}

.overlay {
    display: none;
    position: fixed;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1035 !important;
    opacity: 0;
    transition: all 0.5s ease-in-out;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    touch-action: pan-y;
    user-select: none;
    -webkit-user-drag: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.e-animated {
    -webkit-animation-duration: 0.6s;
    animation-duration: 0.6s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

/* Collapsible vertical menu */

.collapsible-vertical {
    .sidebar-theme {
        background-color: #fff;
    }
    .sidebar-wrapper {
        top: 128px;
        height: calc(100vh - 150px);
        border-radius: 6px;
        #sidebar {
            ul {
                &.menu-categories {
                    li {
                        &.menu {
                            color: #888ea8;
                            &.active > a {
                                background: transparent;
                                box-shadow: none;
                                span {
                                    color: #060818;
                                }
                                svg {
                                    color: #4361ee;
                                }
                            }
                            > .dropdown-toggle {
                                padding: 9px 20px;
                                color: #888ea8;
                                svg {
                                    color: #515365;
                                }
                                &[aria-expanded='true']:not([data-active='true']) {
                                    background: transparent;
                                    box-shadow: none;
                                    color: #060818;
                                    svg {
                                        color: #4361ee;
                                    }
                                }
                                svg {
                                    stroke-width: 2;
                                }
                                &:hover {
                                    background: transparent;
                                    box-shadow: none;
                                    color: #4361ee;
                                    svg {
                                        color: #4361ee;
                                    }
                                }
                                &.dropdown-toggle:not([data-active='true']):not([aria-expanded='true']):hover {
                                    background: transparent;
                                    box-shadow: none;
                                }
                            }
                        }
                    }
                    ul {
                        &.submenu {
                            > li {
                                &.active {
                                    &::before {
                                        content: '';
                                        position: absolute;
                                        background-color: #0000000a;
                                        width: 15px;
                                        height: 40px;
                                        width: 100%;
                                        left: 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    .shadow-bottom {
        display: none;
    }
    .sidebar-closed {
        &.main-container {
            padding: 0 0 0 16px;
        }
        > .sidebar-wrapper {
            width: 52px;
            left: 16px;
            background-color: #fff;
            &:hover {
                width: 212px;
                .collapse.show {
                    display: block;
                }
                #sidebar {
                    ul {
                        &.menu-categories {
                            li {
                                &.menu {
                                    > .dropdown-toggle {
                                        padding: 9px 20px;
                                        transition: 0.6s;
                                        svg.feather-chevron-right {
                                            display: inline-block;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        .sidebar-wrapper {
            -webkit-box-shadow: 18px 20px 10.3px -23px rgb(0, 0, 0, 0.15), -18px 20px 26px -23px rgba(0, 0, 0, 0.1), -17px 20px 53px -23px rgba(0, 0, 0, 0.124),
                -19px 20px 109.1px -23px rgba(0, 0, 0, 0.152), -17px 20px 299px -23px rgba(0, 0, 0, 0.22);
            -moz-box-shadow: 18px 20px 10.3px -23px rgb(0, 0, 0, 0.15), -18px 20px 26px -23px rgba(0, 0, 0, 0.1), -17px 20px 53px -23px rgba(0, 0, 0, 0.124),
                -19px 20px 109.1px -23px rgba(0, 0, 0, 0.152), -17px 20px 299px -23px rgba(0, 0, 0, 0.22);
            box-shadow: 18px 20px 10.3px -23px rgb(0, 0, 0, 0.15), -18px 20px 26px -23px rgba(0, 0, 0, 0.1), -17px 20px 53px -23px rgba(0, 0, 0, 0.124), -19px 20px 109.1px -23px rgba(0, 0, 0, 0.152),
                -17px 20px 299px -23px rgba(0, 0, 0, 0.22);
            .collapse.show {
                display: none;
            }
        }
        > #content {
            margin-left: 54px;
        }

        #sidebar {
            ul {
                &.menu-categories {
                    li {
                        &.menu {
                            > .dropdown-toggle {
                                padding: 12px 16px;
                                transition: 0.6s;
                                position: relative;
                                svg {
                                    &.feather-chevron-right {
                                        display: none;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    #sidebar {
        ul {
            &.menu-categories {
                padding-right: 0;
                &.ps {
                    height: calc(100vh - 150px) !important;
                    border: 0px;
                }
            }
        }
    }
}

@media (max-width: 991px) {
    .collapsible-vertical {
        .sidebar-wrapper {
            top: 0px;
        }
        .collapsible-vertical-mobile {
            .sidebar-wrapper {
                left: auto !important;
                width: 255px !important;
            }
            .overlay {
                display: block;
                opacity: 0.7;
            }
        }

        .sidebar-closed {
            > .sidebar-wrapper {
                width: 255px;
                top: 0px;
                left: 0px;
            }
            .sidebar-wrapper {
                .collapse {
                    &.show {
                        display: block;
                    }
                }
            }
            #sidebar ul.menu-categories li.menu > .dropdown-toggle {
                padding: 9px 20px;
            }
            &.main-container {
                padding: 0px !important;
            }
            #sidebar ul.menu-categories li.menu > .dropdown-toggle svg.feather-chevron-right {
                display: block;
            }

            &.collapsible-vertical-mobile {
                .sidebar-wrapper {
                    left: -52px !important;
                    width: 0 !important;
                }
                .overlay.show {
                    display: none !important;
                    opacity: 0 !important;
                }
            }
        }

        #sidebar {
            ul {
                &.menu-categories {
                    &.ps {
                        height: calc(100vh - 70px) !important;
                    }
                }
            }
        }
    }
}

@-webkit-keyframes e-fadeInUp {
    0% {
        opacity: 0;
        margin-top: 10px;
    }

    100% {
        opacity: 1;
        margin-top: 0;
    }
}

@keyframes e-fadeInUp {
    0% {
        opacity: 0;
        margin-top: 10px;
    }

    100% {
        opacity: 1;
        margin-top: 0;
    }
}

.e-fadeInUp {
    -webkit-animation-name: e-fadeInUp;
    animation-name: e-fadeInUp;
}

#sidebar ul.menu-categories .ps__rail-y {
    right: -4px !important;
}

//	=================
//    	Imports
//	=================

@import '../base/color_variables'; // Color Variables
@import '../base/mixins'; // Mixins Variables
@import '../base/utilities'; // Utilities Variables

html {
    min-height: 100%;
}

body {
    color: #888ea8;
    height: 100%;
    font-size: 0.912rem;
    background: #f1f2f3;
    overflow-x: hidden;
    overflow-y: auto;
    letter-spacing: 0.0312rem;
    font-family: 'Sarabun','Noto Sans', sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: #3b3f5c;
}

:focus {
    outline: none;
}

::-moz-selection {
    /* Code for Firefox */
    color: #4361ee;
    background: transparent;
}

::selection {
    color: #4361ee;
    background: transparent;
}

p {
    margin-top: 0;
    margin-bottom: 0.625rem;
    color: #515365;
}

hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border-top: 1px solid #f1f2f3;
}

strong {
    font-weight: 600;
}

code {
    color: #e7515a;
}

/*Page title*/

.page-header {
    border: 0;
    margin: 0;

    &:before {
        display: table;
        content: '';
        line-height: 0;
    }

    &:after {
        display: table;
        content: '';
        line-height: 0;
        clear: both;
    }
}

.page-title {
    h3 {
        margin: 0;
        margin-bottom: 0;
        font-size: 15px;
        color: #3b3f5c;
        font-weight: 700;
    }

    span {
        display: block;
        font-size: 11px;
        color: #888;
        font-weight: normal;
    }
}

.main-container {
    min-height: 100vh;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
    -ms-flex-direction: row;
    flex-direction: row;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    -webkit-box-pack: start;
    -ms-flex-pack: start;
    justify-content: flex-start;
    padding: 0 0 0 16px;
}

#container.fixed-header {
    margin-top: 56px;
}

#content {
    position: relative;
    width: 50%;
    flex-grow: 8;
    margin-top: 108px;
    margin-bottom: 0;
    margin-left: 212px;
    transition: 0.6s;
}

.main-container-fluid > .main-content > .container {
    float: left;
    width: 100%;
}

#content > .wrapper {
    -webkit-transition: margin ease-in-out 0.1s;
    -moz-transition: margin ease-in-out 0.1s;
    -o-transition: margin ease-in-out 0.1s;
    transition: margin ease-in-out 0.1s;
    position: relative;
}

.panel {
    padding: 0;
    margin-top: 0;
    margin-bottom: 0;
    border-radius: 6px;
    border: 1px solid #e0e6ed;
    box-shadow: 0 0 40px 0 rgba(94, 92, 154, 0.06);
}

.layout-top-spacing {
    margin-top: 20px;
}

.layout-spacing {
    padding-bottom: 40px;
}

.layout-px-spacing {
    padding: 0 10px 0 10px !important;
    min-height: calc(100vh - 170px) !important;
    margin-top: -10px;
}

.sidebar-closed .layout-px-spacing {
    padding: 0 16px !important;
}

.seperator-header {
    background: transparent;
    box-shadow: none;
    margin-bottom: 40px;
    border-radius: 0;

    h4 {
        margin-bottom: 0;
        line-height: 1.4;
        padding: 5px 8px;
        font-size: 15px;
        border-radius: 4px;
        letter-spacing: 1px;
        display: inline-block;
        background: rgba(0, 150, 136, 0.26);
        color: #009688;
        font-weight: 500;
    }
}

.panel-body-area {
    padding: 20px;
    position: relative;
    background-color: #fff;
    box-shadow: 0 0 40px 0 rgb(94 92 154 / 6%);
}

.content-area {
    max-width: 58.333333%;
    margin-left: 80px;
}

//  ===============================
//	     _structure Imports
//  ===============================

@import '_structure/_navbar';
@import '_structure/_sidebar';

/*  
    ======================
        Footer-wrapper
    ======================
*/

.footer-wrapper {
    padding: 10px 20px 10px 18px;
    display: inline-block;
    background: transparent;
    font-weight: 600;
    font-size: 12px;
    width: 100%;
    border-top-left-radius: 6px;
    display: flex;
    justify-content: space-between;

    .footer-section {
        p {
            margin-bottom: 0;
            color: #515365;
            font-size: 13px;
            letter-spacing: 1px;

            a {
                color: #515365;
            }
        }

        svg {
            color: #e7515a;
            fill: rgba(231, 81, 90, 0.4196078431);
            width: 15px;
            height: 15px;
            vertical-align: text-top;
        }
    }
}

.main-container.sidebar-closed .footer-wrapper {
    border-radius: 0;
}

.panel {
    border: 1px solid #e0e6ed;
    box-shadow: 0 0 40px 0 rgb(94 92 154 / 6%);
    border-radius: 6px;
    background: #fff;

    .panel-heading {
        padding: 20px;

        h4 {
            font-size: 17px;
            margin: 0;
            color: #3b3f5c;
            font-weight: 600;
        }
    }

    .panel-body {
        padding: 20px;
    }
}

@import '_structure/_media-query';

<template>
    <!--  BEGIN SIDEBAR  -->
    <div class="sidebar-wrapper sidebar-theme">
        <nav ref="menu" id="sidebar">
            <div class="shadow-bottom"></div>

            <perfect-scrollbar class="list-unstyled menu-categories" tag="ul" :options="{
                wheelSpeed: 0.5,
                swipeEasing: !0,
                minScrollbarLength: 40,
                maxScrollbarLength: 300,
                suppressScrollX: true,
            }">
                <!-- Trang chủ -->
                <router-link tag="li" :to="link_home" class="menu" @click.native="toggleMobileMenu">
                    <a class="dropdown-toggle">
                        <div class>
                            <i data-feather="home"></i>
                            <span>{{ $t("dashboard") }}</span>
                        </div>
                    </a>
                </router-link>

                <!-- Quản lý <PERSON>h mục hệ thống -->
                <li class="menu">
                    <a href="#master" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="book"></i>
                            <span>{{ $t("master") }}</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>
                    <b-collapse id="master" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <router-link tag="li" to="/product-category" @click.native="toggleMobileMenu"
                                v-permission="['product_list']">
                                <a>Nhóm sản phẩm</a>
                            </router-link>
                            <router-link tag="li" to="/product" @click.native="toggleMobileMenu"
                                v-permission="['product_list']">
                                <a>Sản phẩm</a>
                            </router-link>
                            <router-link tag="li" to="/config-business-area" @click.native="toggleMobileMenu"
                                v-permission="['product_config_range_for_team']">
                                <a>Thị trường KD</a>
                            </router-link>
                            <router-link tag="li" to="/schools" @click.native="toggleMobileMenu" v-permission="[
                                'school_list_management',
                                'school_list',
                            ]">
                                <a>Trường</a>
                            </router-link>
                            <router-link tag="li" to="/holiday" @click.native="toggleMobileMenu">
                                <a>Ngày nghỉ lễ</a>
                            </router-link>
                            <router-link tag="li" to="/work-day-symbol" @click.native="toggleMobileMenu">
                                <a>Ký hiệu ngày công</a>
                            </router-link>
                            <!-- <router-link
                tag="li"
                to="/quarterly-goals"
                @click.native="toggleMobileMenu"
                v-permission="['quarterly_goal_list']"
              >
                <a>Mục tiêu quý </a>
              </router-link> -->
                            <router-link tag="li" to="/month-goals" @click.native="toggleMobileMenu"
                                v-permission="['month_goal_list']">
                                <a>Mục tiêu tháng </a>
                            </router-link>
                            <router-link tag="li" to="/absence-letter-type" @click.native="toggleMobileMenu">
                                <a>Ngày nghỉ phép</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>

                <!-- Quản lý nhân sự -->
                <li class="menu">
                    <a href="#users" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="users"></i>
                            <span>{{ $t("users") }}</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>
                    <b-collapse id="users" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <router-link tag="li" to="/department" @click.native="toggleMobileMenu"
                                v-permission="['department_list']">
                                <a>Phòng ban/bộ phận</a>
                            </router-link>

                            <router-link tag="li" to="/position" @click.native="toggleMobileMenu"
                                v-permission="['positions_list']">
                                <a>Vị trí / chức vụ</a>
                            </router-link>
                            <router-link tag="li" to="/user" @click.native="toggleMobileMenu"
                                v-permission="['user_list']">
                                <a>Nhân sự</a>
                            </router-link>
                            <router-link tag="li" to="/user/status" @click.native="toggleMobileMenu"
                                v-permission="['user_status']">
                                <a>Tình hình nhân sự</a>
                            </router-link>
                            <router-link v-permission="['role_list']" tag="li" to="/roles"
                                @click.native="toggleMobileMenu">
                                <a>Nhóm người dùng</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>
                <!-- Quản lý Công -->
                <li class="menu">
                    <a href="#timesheets" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="grid"></i>
                            <span>Chấm công</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>
                    <b-collapse id="timesheets" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <router-link tag="li" to="/hanet-cam-user" @click.native="toggleMobileMenu"
                                v-role="['admin']">
                                <a>Hanet camAI</a>
                            </router-link>
                            <router-link tag="li" to="/time-sheet" @click.native="toggleMobileMenu">
                                <a>Bảng công</a>
                            </router-link>
                            <router-link tag="li" to="/checkin-checkout" @click.native="toggleMobileMenu">
                                <a>CheckIn/Out</a>
                            </router-link>
                            <router-link tag="li" to="/checkin-checkout-online" @click.native="toggleMobileMenu">
                                <a>CheckIn/Out Online</a>
                            </router-link>
                            <router-link tag="li" to="/absence-year" @click.native="toggleMobileMenu">
                                <a>Quản lý phép năm</a>
                            </router-link>
                            <router-link tag="li" to="/absence-letters" @click.native="toggleMobileMenu">
                                <a>Xin nghỉ phép</a>
                            </router-link>
                            <router-link tag="li" to="/checkin-checkout-reject" @click.native="toggleMobileMenu"
                                v-role="['hr', 'hr_leader']">
                                <a>Xác nhận công</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>
                <!--Quản lý đăng ký công tác-->
                <li class="menu">
                    <a href="#business_plan" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="truck"></i>
                            <span>Công tác</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>

                    <b-collapse id="business_plan" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <router-link tag="li" to="/business-plan-list" @click.native="toggleMobileMenu">
                                <a>Đăng ký công tác</a>
                            </router-link>
                            <router-link tag="li" to="/business-report-list" @click.native="toggleMobileMenu">
                                <a>Báo cáo công tác</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>
                <!--HCNS-->
                <li class="menu" v-permission="['folder_list', 'property_list', 'sub_property_list']">
                    <a href="#hr" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="file-text"></i>
                            <span>Văn phòng</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>
                    <b-collapse id="hr" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <router-link v-permission="['folder_list']" tag="li" to="/folder"
                                @click.native="toggleMobileMenu">
                                <a>Tài liệu</a>
                            </router-link>
                            <router-link v-permission="['property_list']" tag="li" to="/property-type"
                                @click.native="toggleMobileMenu">
                                <a>Loại tài sản</a>
                            </router-link>
                            <router-link v-permission="['property_list']" tag="li" to="/property-sub"
                                @click.native="toggleMobileMenu">
                                <a>Mô tả tải sản</a>
                            </router-link>
                            <router-link v-permission="['sub_property_list']" tag="li" to="/property"
                                @click.native="toggleMobileMenu">
                                <a>Tài sản</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>
                <!--KPI-->
                <li class="menu">
                    <a href="#kpi" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="compass"></i>
                            <span>Quản lý KPI</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>
                    <b-collapse id="kpi" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <!-- <router-link
                tag="li"
                to="/master-kpi"
                @click.native="toggleMobileMenu"
                v-permission="['m_kpi_list']"
              >
                <a>Danh mục KPI</a>
              </router-link>
              <router-link
                tag="li"
                to="/list-kpi-register"
                @click.native="toggleMobileMenu"
              >
                <a>Đăng ký KPI cũ</a>
              </router-link>
              <router-link
                tag="li"
                to="/assessment-list-kpi"
                @click.native="toggleMobileMenu"
              >
                <a>Đánh giá KPI cũ</a>
              </router-link> -->
                            <router-link tag="li" to="/upgrade-register-kpi-list" @click.native="toggleMobileMenu">
                                <a>Đăng ký KPI</a>
                            </router-link>
                            <router-link tag="li" to="/upgrade-kpi-assessment-list" @click.native="toggleMobileMenu">
                                <a>Đánh giá KPI</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>
                <!-- ChangeLog -->
                <li class="menu">
                    <a href="#changelog" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="file-minus"></i>
                            <span>Nhật ký</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>
                    <b-collapse id="changelog" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <router-link tag="li" to="/changelog-list" @click.native="toggleMobileMenu">
                                <a>Nhật ký</a>
                            </router-link>
                            <router-link tag="li" to="/changelog-management" @click.native="toggleMobileMenu">
                                <a>Quản lý nhật ký</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>
                <!-- Quản lý khách hàng -->
                <li class="menu" v-permission="[
                    'client_list',
                    'contract_list',
                    'expense_list',
                    'payment_list',
                ]">
                    <a href="#customer" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="layers"></i>
                            <span>{{ $t("customer") }}</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>
                    <b-collapse id="customer" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <router-link tag="li" to="/client" @click.native="toggleMobileMenu"
                                v-permission="['client_list']">
                                <a>Khách hàng</a>
                            </router-link>
                            <!-- <router-link
                                tag="li"
                                to="/ke-client"
                                @click.native="toggleMobileMenu"
                            >
                                <a>Khách hàng KE</a>
                            </router-link> -->
                            <router-link tag="li" to="/contract" @click.native="toggleMobileMenu"
                                v-permission="['contract_list']">
                                <a>Hợp đồng Base</a>
                            </router-link>
                            <router-link tag="li" to="/ke-contract" @click.native="toggleMobileMenu"
                                v-permission="['contract_list']">
                                <a>Hợp đồng KE</a>
                            </router-link>
                            <router-link tag="li" to="/expense" @click.native="toggleMobileMenu"
                                v-permission="['expense_list']">
                                <a>Chi phí</a>
                            </router-link>
                            <router-link tag="li" to="/payment" @click.native="toggleMobileMenu"
                                v-permission="['payment_list']">
                                <a>Thanh toán</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>

                <!-- Quản lý đối tác -->
                <router-link tag="li" to="/partners" class="menu" @click.native="toggleMobileMenu" v-permission="['partner_list']">
                    <a class="dropdown-toggle">
                        <div class>
                            <i data-feather="briefcase"></i>
                            <span>Quản lý đối tác</span>
                        </div>
                    </a>
                </router-link>

                <!-- Quản lý tickets -->
                <router-link tag="li" to="/tasks-management/projects" class="menu" @click.native="toggleMobileMenu">
                    <a class="dropdown-toggle">
                        <div class>
                            <i data-feather="bookmark"></i>
                            <span>Quản lý công việc</span>
                        </div>
                    </a>
                </router-link>

                <!-- Cuộc gọi khách hàng -->
                <router-link tag="li" to="/call-center" class="menu" @click.native="toggleMobileMenu">
                    <a class="dropdown-toggle">
                        <div class>
                            <i data-feather="phone"></i>
                            <span>Cuộc gọi k/h</span>
                        </div>
                    </a>
                </router-link>

                <!-- Báo cáo thống kê, chỗ này sẽ link đến 1 trang list các cáo cáo -->
                <router-link tag="li" to="/report" class="menu" @click.native="toggleMobileMenu"
                    v-permission="['report_list']">
                    <a class="dropdown-toggle">
                        <div class>
                            <i data-feather="credit-card"></i>
                            <span>{{ $t("report") }}</span>
                        </div>
                    </a>
                </router-link>

                <!--KE-->
                <li class="menu" v-permission="[
                    'supplier_list',
                    'warehouse_list',
                    'equipment_type_list',
                    'equipment_list',
                    'kidsenglish_exwarehouse_list',
                    'kidsenglish_exwarehouse_add',
                    // 'kidsenglish_sales_packages_list',
                    // 'kidsenglish_sales_packages_add',
                    'account_allocation_list',
                    'account_allocation_add',
                    'account_allocation_edit',
                    'account_allocation_approve',
                    'account_allocation_delete',
                ]">
                    <a href="#ke" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="layers"></i>
                            <span>KIDSEnglish</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>
                    <b-collapse id="ke" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <router-link tag="li" to="/supplier" @click.native="toggleMobileMenu"
                                v-permission="['supplier_list']">
                                <a>Nhà cung cấp</a>
                            </router-link>
                            <router-link tag="li" to="/warehouse" v-permission="['warehouse_list']"
                                @click.native="toggleMobileMenu">
                                <a>Kho</a>
                            </router-link>
                            <router-link tag="li" to="/equipment-type" @click.native="toggleMobileMenu"
                                v-permission="['equipment_type_list']">
                                <a>Loại hàng hóa</a>
                            </router-link>

                            <router-link tag="li" to="/sales-packages" @click.native="toggleMobileMenu" v-if="
                                hasPermission([
                                    'kidsenglish_sales_packages_list',
                                    'kidsenglish_sales_packages_add',
                                ])
                            ">
                                <a>Gói bán hàng KE</a>
                            </router-link>

                            <router-link tag="li" to="/kidsenglish/account-allocation" @click.native="toggleMobileMenu"
                                v-if="
                                    hasPermission([
                                        'account_allocation_list',
                                        'account_allocation_add',
                                        'account_allocation_edit',
                                        'account_allocation_approve',
                                    ])
                                ">
                                <a>Cấp tài khoản</a>
                            </router-link>
                            <router-link tag="li" to="/equipment" @click.native="toggleMobileMenu"
                                v-permission="['equipment_list']">
                                <a>Nhập kho</a>
                            </router-link>
                            <router-link tag="li" to="/ex-warehouse" @click.native="toggleMobileMenu" v-permission="[
                                'kidsenglish_exwarehouse_list',
                                'kidsenglish_exwarehouse_add',
                            ]">
                                <a>Xuất kho</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>

                <router-link tag="li" to="/survey-list" class="menu" @click.native="toggleMobileMenu"
                    v-if="!hasPermission(['survey_exam_management'])">
                    <a class="dropdown-toggle">
                        <div class>
                            <i data-feather="file-text"></i>
                            <span>Khảo sát</span>
                        </div>
                    </a>
                </router-link>

                <li class="menu" v-permission="[
                    'survey_category_list',
                    'survey_question_list',
                    'survey_exam_list',
                    'survey_exam_management',
                ]">
                    <a href="#survey" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="file-text"></i>
                            <span>Khảo sát</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>
                    <b-collapse id="survey" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <router-link tag="li" to="/survey/category" @click.native="toggleMobileMenu"
                                v-permission="['survey_category_list']">
                                <a>Danh mục</a>
                            </router-link>
                            <router-link tag="li" to="/survey/question" @click.native="toggleMobileMenu"
                                v-permission="['survey_question_list']">
                                <a>Câu hỏi</a>
                            </router-link>
                            <router-link tag="li" to="/survey/exam" @click.native="toggleMobileMenu"
                                v-permission="['survey_exam_list']">
                                <a>Bài khảo sát</a>
                            </router-link>
                            <router-link tag="li" to="/survey/exam/history" @click.native="toggleMobileMenu"
                                v-permission="['survey_exam_management']">
                                <a>Lịch sử khảo sát</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>

                <!-- Thứ 4 chia sẻ -->
                <router-link tag="li" to="/wednesday-share" class="menu" @click.native="toggleMobileMenu">
                    <a class="dropdown-toggle">
                        <div class>
                            <i data-feather="heart"></i>
                            <span>Thứ 4 chia sẻ</span>
                        </div>
                    </a>
                </router-link>
                <!-- Cấu hình hệ thống -->
                <li class="menu" v-permission="['config_system']">
                    <a href="#config-system" v-b-toggle class="dropdown-toggle" @click.prevent>
                        <div class>
                            <i data-feather="layers"></i>
                            <span>{{ "Cấu hình HT" }}</span>
                        </div>
                        <div>
                            <i data-feather="chevron-right"></i>
                        </div>
                    </a>
                    <b-collapse id="config-system" accordion="menu">
                        <ul class="collapse submenu list-unstyled show">
                            <router-link tag="li" to="/setting-happy-text" @click.native="toggleMobileMenu">
                                <a>Lời chúc</a>
                            </router-link>
                            <router-link tag="li" to="/config-happy-birthday" @click.native="toggleMobileMenu">
                                <a>CMSN</a>
                            </router-link>
                            <router-link tag="li" to="/setting-receive-email" @click.native="toggleMobileMenu">
                                <a>Cấu hình nhận Email</a>
                            </router-link>
                            <router-link tag="li" to="/setting-feedback-object" @click.native="toggleMobileMenu">
                                <a>Cấu hình Feedback</a>
                            </router-link>
                            <router-link tag="li" to="/setting-kpi-assessment-time" @click.native="toggleMobileMenu">
                                <a>Cấu hình thời gian đánh giá KPI</a>
                            </router-link>
                            <router-link tag="li" to="/setting-explain" @click.native="toggleMobileMenu">
                                <a>Cấu hình giải trình</a>
                            </router-link>
                        </ul>
                    </b-collapse>
                </li>
                <!-- Quản lý hiển thị App -->
                <router-link v-role="['admin', 'rd']" tag="li" to="/app-display" class="menu"
                    @click.native="toggleMobileMenu">
                    <a class="dropdown-toggle">
                        <div class>
                            <i data-feather="tablet"></i>
                            <span>Quản lý HT App</span>
                        </div>
                    </a>
                </router-link>
                <router-link v-permission="['question_list']" tag="li" to="/frequently-question-list" class="menu"
                    @click.native="toggleMobileMenu">
                    <a class="dropdown-toggle">
                        <div class>
                            <i class="far fa-question-circle"></i>
                            <span>CH thường gặp</span>
                        </div>
                    </a>
                </router-link>
            </perfect-scrollbar>
        </nav>
    </div>
    <!--  END SIDEBAR  -->
</template>
<script>
    import permission from "@/directive/permission/index.js";
    import checkPermission from "@/helpers/permission";
    import role from "@/directive/role/index.js";
    import feather from "feather-icons";
    import c from "@/helpers/common";
    import "@/views/style.css";

    export default {
        directives: { permission, role },
        components: { feather },
        data() {
            return {
                menu_collapse: "dashboard",
                link_home: "/",
            };
        },
        watch: {
            $route(to) {
                let temp_path = to.meta.children ? to.meta.parent : to.path;
                // const selector = document.querySelector('#sidebar a[href="' + to.path + '"]');
                const selector = document.querySelector(
                    '#sidebar a[href="' + temp_path + '"]'
                );
                if (selector) {
                    const ul = selector.closest("ul.collapse");
                    if (!ul) {
                        const ele = document.querySelector(
                            ".dropdown-toggle.not-collapsed"
                        );
                        if (ele) {
                            ele.click();
                        }
                    }
                }
            },
        },
        mounted() {
            feather.replace();
            // default menu selection on refresh
            const selector = document.querySelector(
                '#sidebar a[href="' + window.location.pathname + '"]'
            );
            if (selector) {
                const ul = selector.closest("ul.collapse");
                if (ul) {
                    let ele = ul
                        .closest("li.menu")
                        .querySelectorAll(".dropdown-toggle");
                    if (ele) {
                        ele = ele[0];
                        setTimeout(() => {
                            ele.click();
                        });
                    }
                } else {
                    selector.click();
                }
            }
        },
        methods: {
            toggleMobileMenu() {
                if (window.innerWidth < 991) {
                    this.$store.commit("toggleSideBar", true);
                }
            },
            hasPermission: function (permissions) {
                return checkPermission(permissions);
            },
        },
        created() {
            let permissions = c.session().user.permissions;
            if (
                (permissions.indexOf("dashboard_public") > -1 &&
                    permissions.indexOf("dashboard_private") > -1) ||
                permissions.indexOf("dashboard_private") > -1
            ) {
                this.link_home = "/";
            } else {
                this.link_home = "/dashboard";
            }
        },
    };
</script>
<style scoped>
    #sidebar ul.menu-categories li.menu>.dropdown-toggle svg {
        margin-right: 5px !important;
    }
</style>

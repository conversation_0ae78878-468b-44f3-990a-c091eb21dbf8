import { currentUser } from "../../helpers/auth";
const user = currentUser();

export default {
    state: {
        currentUser: user,
        isLoggedIn: !!user,
        loading: false,
        authError: null,
        isExpired: false,
        account_type: "app",
    },
    getters: {
        IS_LOADING: (state) => {
            return state.loading;
        },
        IS_LOGGEND_IN: (state) => {
            return state.isLoggedIn;
        },
        CURRENT_USER: (state) => {
            return state.currentUser;
        },
        AUTH_ERROR: (state) => {
            return state.authError;
        },
        IS_EXPIRED: (state) => {
            return state.isExpired;
        },
    },
    mutations: {
        LOGIN: (state) => {
            state.loading = true;
            state.authError = null;
        },
        LOGIN_SUCCESS: (state, payload) => {
            state.isExpired = false;
            state.authError = null;
            state.isLoggedIn = true;
            state.loading = false;
            state.currentUser = Object.assign(
                {},
                payload.user,
                { token: payload.access_token },
                { role: payload.role },
                { permissions: payload.permissions }
            );
            state.currentInfos = Object.assign({}, payload.infos);
            state.me = Object.assign({}, payload.me);
            state.notifications = Object.assign({}, payload.notifications);
            

            localStorage.setItem("user", JSON.stringify(state.currentUser));
            localStorage.setItem("infos", JSON.stringify(state.currentInfos));
            localStorage.setItem("me", JSON.stringify(state.me));
            localStorage.setItem(
                "notifications",
                JSON.stringify(state.notifications)
            );
            localStorage.setItem("i18n_locale", "vi");
            localStorage.setItem("account_type", state.account_type);
        },
        LOGIN_FAILED: (state, payload) => {
            state.authError = false;
            state.loading = false;
        },
        LOGOUT: (state) => {
            localStorage.removeItem("user");
            state.isLoggedIn = false;
            state.currentUser = null;
            state.account_type = null;
        },
        setIsExpired: (state, payload) => {
            state.isExpired = payload;
        },
        PARTNER_LOGIN: (state) => {
            state.loading = true;
            state.authError = null;
        },
        PARTNER_LOGIN_SUCCESS: (state, payload) => {
            state.isExpired = false;
            state.authError = null;
            state.isLoggedIn = true;
            state.loading = false;
            state.currentUser = Object.assign(
                {},
                payload.partner,
                { token: payload.access_token },
                { role: payload.role },
                { permissions: payload.permissions }
            );
            state.currentInfos = Object.assign({}, payload.infos);
            state.me = Object.assign({}, payload.me);
            state.notifications = Object.assign({}, payload.notifications);

            localStorage.setItem("partner", JSON.stringify(state.currentUser));
            localStorage.setItem("infos", JSON.stringify(state.currentInfos));
            localStorage.setItem("me", JSON.stringify(state.me));
            localStorage.setItem(
                "notifications",
                JSON.stringify(state.notifications)
            );
            localStorage.setItem("i18n_locale", "vi");
        },
        PARTNER_LOGIN_FAILED: (state, payload) => {
            state.authError = false;
            state.loading = false;
        },
        PARTNER_LOGOUT: (state) => {
            localStorage.removeItem("partner");
            state.isLoggedIn = false;
            state.currentUser = null;
        },
    },
    actions: {
        LOGIN: (context) => {
            context.commit("LOGIN");
        },
        PARTNER_LOGIN: (context) => {
            context.commit("PARTNER_LOGIN");
        },
    },
};

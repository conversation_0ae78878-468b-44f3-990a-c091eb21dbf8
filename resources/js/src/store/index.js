import Vue from "vue";
import Vuex from "vuex";
import i18n from "../i18n";
import Auth from "./modules/auth";
import PartnerAuth from "./modules/partner_auth";

Vue.use(Vuex);

export default new Vuex.Store({
    state: {
        layout: "app",
        isUpdateDeviceId: false,
        is_show_sidebar: true,
        is_show_search: false,
        is_loading: false,
        is_dark_mode: false,
        dark_mode: "light",
        locale: null,
        menu_style: "vertical",
        layout_style: "full",
        snackBar: {
            type: null,
            msg: null,
            position: null,
        },
        countryList: [
            { code: "vi", name: "Việt nam" },
            { code: "en", name: "English" },
            { code: "fr", name: "French" },
            { code: "de", name: "German" },
            { code: "zh", name: "Chinese" },
        ],
        ticketSideBar: {
            addUrl: "",
            projectId: "",
            projectName: "",
            projectCode: "",
        },
    },
    mutations: {
        setTicketSideBar(state, payload) {
            state.ticketSideBar = { ...state.ticketSideBar, ...payload };
        },
        setUpdateDeviceId(state, payload) {
            state.isUpdateDeviceId = payload;
        },
        setLayout(state, payload) {
            state.layout = payload;
        },
        setSnackBar(state, value) {
            state.snackBar = {
                ...value,
                position: value.position || "bottom-center",
            };
            setTimeout(() => {
                state.snackBar = {
                    type: null,
                    msg: null,
                    position: null,
                };
            }, value.timeout ?? 4000);
        },
        toggleLoading(state, status) {
            state.is_loading = status;
            const body = document.querySelector("body");
            // Remove any existing overlay before adding a new one
            const oldOverlay = document.getElementById("global-loading-overlay");
            if (oldOverlay) oldOverlay.remove();
            if (status) {
                // Add overlay to lock the screen
                const overlay = document.createElement("div");
                overlay.id = "global-loading-overlay";
                overlay.style.position = "fixed";
                overlay.style.top = 0;
                overlay.style.left = 0;
                overlay.style.width = "100vw";
                overlay.style.height = "100vh";
                overlay.style.background = "rgba(0,0,0,0.3)";
                overlay.style.zIndex = 9999;
                overlay.style.display = "flex";
                overlay.style.alignItems = "center";
                overlay.style.justifyContent = "center";
                // overlay.innerHTML = '<div class="spinner" style="border: 8px solid #f3f3f3; border-top: 8px solid #3498db; border-radius: 50%; width: 60px; height: 60px; animation: spin 1s linear infinite;"></div>';
                document.body.appendChild(overlay);
                // Add spinner animation style
                if (!document.getElementById("global-loading-style")) {
                    const style = document.createElement("style");
                    style.id = "global-loading-style";
                    style.innerHTML = `@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }`;
                    document.head.appendChild(style);
                }
                body.style.overflow = "hidden";
            } else {
                // Remove overlay
                const overlay = document.getElementById("global-loading-overlay");
                if (overlay) overlay.remove();
                body.style.overflow = "";
            }
        },
        toggleSideBar(state, value) {
            state.is_show_sidebar = value;
        },
        toggleSearch(state, value) {
            state.is_show_search = value;
        },
        toggleLocale(state, value) {
            value = value || "vi";
            i18n.locale = value;
            localStorage.setItem("i18n_locale", value);
            state.locale = value;
        },

        toggleDarkMode(state, value) {
            //light|dark|system
            value = value || "light";
            localStorage.setItem("dark_mode", value);
            state.dark_mode = value;
            if (value == "light") {
                state.is_dark_mode = false;
            } else if (value == "dark") {
                state.is_dark_mode = true;
            } else if (value == "system") {
                if (
                    window.matchMedia &&
                    window.matchMedia("(prefers-color-scheme: dark)").matches
                ) {
                    state.is_dark_mode = true;
                } else {
                    state.is_dark_mode = false;
                }
            }

            if (state.is_dark_mode) {
                document.querySelector("body").classList.add("dark");
            } else {
                document.querySelector("body").classList.remove("dark");
            }
        },

        toggleMenuStyle(state, value) {
            //horizontal|vertical|collapsible-vertical
            value = value || "";
            localStorage.setItem("menu_style", value);
            state.menu_style = value;
            if (!value || value === "vertical") {
                state.is_show_sidebar = true;
            } else if (value === "collapsible-vertical") {
                state.is_show_sidebar = false;
            }
        },

        toggleLayoutStyle(state, value) {
            //boxed-layout|large-boxed-layout|full
            value = value || "";
            localStorage.setItem("layout_style", value);
            state.layout_style = value;
        },
    },
    getters: {
        layout(state) {
            return state.layout;
        },
    },
    actions: {},
    modules: {
        Auth,
        PartnerAuth,
    },
});

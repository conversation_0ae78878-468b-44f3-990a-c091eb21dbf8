<?php

namespace Modules\PartnerManagement\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\PartnerManagement\Models\PartnerLicense;
use Modules\PartnerManagement\Models\PMS\PMSUser;

class QLMNUpdateRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        $id = $this->input('id') ?? $this->get('id');
        $current = null;
        if ($id) {
            $current = PartnerLicense::where('id', $id)
                ->where('status', PartnerLicense::STATUS_ACTIVATED)
                ->first();
        }
        return [
            'id' => [
                'required',
                'exists:partner_licenses,id,status,' . PartnerLicense::STATUS_ACTIVATED,
                function ($attribute, $value, $fail) use ($current) {
                    if ($current && method_exists($current, 'activities')) {
                        $editCount = $current->activities()->count();
                        if ($editCount >= 5) {
                            $fail('License này đã vượt quá số lần chỉnh sửa cho phép (5 lần).');
                        }
                    }
                },
            ],
            'project_account' => [
                'required',
                'string',
                'max:100',
                function ($attribute, $value, $fail) use ($current) {
                    $value = vietec_sanitize_username($value);
                    if ($current && $current->project_account !== $value) {
                        $exists = PMSUser::where('username', $value)->first();
                        if ($exists) {
                            $fail('Tài khoản dự án ' . $value . ' đã tồn tại trong hệ thống.');
                        }
                    }
                },
            ],
            'activated_at' => [
                'nullable',
                'date',
                function ($attribute, $value, $fail) use ($current) {
                    $now = \Carbon\Carbon::now()->startOfDay();
                    if ($current && $current->activated_at) {
                        $activatedAtDb = \Carbon\Carbon::parse($current->activated_at);
                        $activatedAt = \Carbon\Carbon::parse($value)->startOfDay();
                        if ($activatedAtDb->eq($activatedAt)) return;
                        if ($activatedAtDb->diffInDays($now, false) >= 3) {
                            $fail('Kích hoạt từ ngày ' . $activatedAtDb->format('d/m/Y') . ', sau khi kích hoạt 3 ngày, hệ thống không cho phép sửa ngày kích hoạt.');
                            return;
                        }
                        if ($value) {
                            if ($activatedAtDb->diffInDays($value, false) >= 15) {
                                $fail('Kích hoạt từ ngày ' . $activatedAtDb->format('d/m/Y') . ', ngày kich hoạt mới không được cách ngày kích hoạt cũ quá 15 ngày.');
                                return;
                            }

                            if (!$activatedAt->greaterThanOrEqualTo($now)) {
                                $fail('Ngày kích hoạt phải sau hoặc bằng ngày hiện tại' . $activatedAt);
                                return;
                            }
                        }
                    }
                },
            ],
        ];
    }

    public function attributes()
    {
        return [
            'id' => 'ID trường',
            'project_account' => 'Tài khoản dự án',
            'activated_at' => 'Ngày kích hoạt',
        ];
    }
}

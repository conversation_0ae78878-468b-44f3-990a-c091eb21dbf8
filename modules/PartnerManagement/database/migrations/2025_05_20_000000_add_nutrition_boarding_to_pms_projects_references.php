<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddNutritionBoardingToPmsProjectsReferences extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('pms_projects_references', function (Blueprint $table) {
            $table->string('nutrition_boarding')->default('1,2')->after('id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('pms_projects_references', function (Blueprint $table) {
            $table->dropColumn('nutrition_boarding');
        });
    }
}

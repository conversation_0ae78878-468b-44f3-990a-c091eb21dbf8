<?php

namespace Modules\PartnerManagement\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Models\PMS\PMSUser;
use Modules\PartnerManagement\Models\PMS\PMSWard;
use Modules\PartnerManagement\Models\Partner;
use App\Models\Product;
use Modules\PartnerManagement\Events\PartnerNotificationEvent;
use DB;
use Modules\PartnerManagement\Models\PMS\PMSProjectPermission;
use Modules\PartnerManagement\Models\PartnerLicense;
use Carbon\Carbon;

class UpdateLicenseCodeForQLMNJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    protected $id;
    protected $company_id;
    protected $product_id;
    protected $expired_time;
    protected $pms_unit_id;
    protected $pms_account;
    protected $date_start;
    protected $date_end;

    /**
     * Create a new job instance.
     * @param array $data
     */
    public function __construct($id, $company_id, $pms_account, $date_start)
    {
        $this->id = $id;
        $this->company_id = $company_id;
        $this->pms_account = vietec_sanitize_username($pms_account);
        $this->date_start = $date_start;
    }

    /**
     * Execute the job.
     * @return void
     */
    public function handle()
    {
        //DB::beginTransaction();
        try {
            $license = PartnerLicense::where('status', PartnerLicense::STATUS_ACTIVATED)
                ->where('id', $this->id)
                ->where('company_id', $this->company_id)
                ->first();

            if ($license == null) {
                throw new \Exception("Không có giấy phép mã #" . $this->id);
            }

            // Store the original state
            $beforeState = json_encode([
                'id' => $license->id,
                'project_account' => $license->project_account,
                'activated_at' => $license->activated_at,
                'expired_at' => $license->expired_at,
                'status' => $license->status,
                'company_id' => $license->company_id,
                'project_unit_ref' => $license->project_unit_ref
            ]);

            
            // Update the license
            $license->project_account = $this->pms_account;
            $license->activated_at = Carbon::parse($this->date_start)->startOfDay();
            $license->expired_at = Carbon::parse($this->date_start)->startOfDay()->addMonths(intval($license->month));
            $license->save();

            // Store the new state
            $afterState = json_encode([
                'id' => $license->id,
                'project_account' => $license->project_account,
                'activated_at' => $license->activated_at,
                'expired_at' => $license->expired_at,
                'status' => $license->status,
                'company_id' => $license->company_id,
                'project_unit_ref' => $license->project_unit_ref
            ]);


            //DB::commit();

            return [
                'before' => json_decode($beforeState, true),
                'after' => json_decode($afterState, true)
            ];
        } catch (\Exception $e) {
            //DB::rollBack();
            throw $e;
        }
    }
}

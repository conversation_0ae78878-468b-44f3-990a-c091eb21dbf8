<?php

namespace Modules\PartnerManagement\Transformers;

use App\Models\MongoActivity;
use App\Models\Product;
use Illuminate\Notifications\DatabaseNotification;
use League\Fractal\TransformerAbstract;
use Illuminate\Support\Collection;
use Modules\PartnerManagement\Models\PMS\PMSUnit;
use Modules\PartnerManagement\Models\PMS\PMSWard;
use Carbon\Carbon;
use Modules\PartnerManagement\Models\Partner;
use Modules\PartnerManagement\Models\PartnerLicense;

class PartnerActivityLogTransformer extends TransformerAbstract
{
    protected $fieldLabels;

    public function __construct($fieldLabels = [])
    {
        $this->fieldLabels = $fieldLabels;
    }

    public function transform($log)
    {
        // Lấy thông tin causer chi tiết nếu là Partner
        $causerDetail = null;
        if (
            isset($log['causer_type']) &&
            $log['causer_type'] === 'Modules\\PartnerManagement\\Models\\Partner' &&
            isset($log['causer_id'])
        ) {
            $causerDetail = Partner::find($log['causer_id']);
        }
        return [
            'log_id' => $log['_id'],
            'log_name' => $log['log_name'],
            'event' => $log['event'],
            'description' => $log['description'],
            'causer' => $causerDetail ? $causerDetail->name." (".$causerDetail->email.")" : null,
            'subject' => [
                'id' => $log['subject_id'],
                'type' => $this->simplifyType($log['subject_type']),
            ],
            'changes' => $this->formatChanges($log, $log['properties'], $this->fieldLabels),
            'created_at' => $this->formatDate($log['created_at']),
            'updated_at' => $this->formatDate($log['updated_at']),
        ];
    }

    private function simplifyType($type)
    {
        $parts = explode('\\', $type);
        return end($parts);
    }

    private function formatChanges($log, $properties, $fieldLabels = [])
    {
        $changes = [];
        if (isset($properties['attributes'])) {
            foreach ($properties['attributes'] as $key => $newValue) {
                if (isset($properties['attributes'][$key])) {
                    // Sử dụng tên tiếng Việt nếu có trong $fieldLabels, nếu không giữ nguyên key
                    $label = isset($fieldLabels[$key]) ? $fieldLabels[$key] : $key;
                    
                    $oldValue = $properties['old'][$key];
                    // Kiểm tra nếu giá trị có thể là date bằng cách truyền thêm tên field
                    $isDate = $this->isDateValue($properties['old'][$key], $key) || $this->isDateValue($newValue, $key);
                    
                    if($key == 'status'){
                        $oldValue = PartnerLicense::STATUS_LABELS[$oldValue];
                        $newValue = PartnerLicense::STATUS_LABELS[$newValue];
                    }

                    if($key == 'project_unit_ref'){
                        $oldValue = PMSUnit::find($oldValue) ? PMSUnit::find($oldValue)->name : null;
                        $newValue = PMSUnit::find($newValue) ? PMSUnit::find($newValue)->name : null;
                    }

                    if($key == 'project_code'){
                        $oldValue = Product::find($oldValue) ? Product::find($oldValue)->name : null;
                        $newValue = Product::find($newValue) ? Product::find($newValue)->name : null;
                    }


                    $changes[$label] = [
                        'old' => $isDate ? $this->formatDate($oldValue) : $oldValue,
                        'new' => $isDate ? $this->formatDate($newValue) : $newValue,
                    ];
                }
            }
        }
        return $changes;
    }

    // Hàm kiểm tra giá trị có phải là date không
    private function isDateValue($value, $key = null)
    {
        // Các field được xác định là date
        $dateFields = ['updated_at', 'created_at', 'assigned_at', 'expired_at','activated_at'];
        
        // Nếu field name nằm trong danh sách date fields, return true
        if ($key !== null && in_array($key, $dateFields)) {
            return true;
        }

        // Nếu không có giá trị, return false
        if (empty($value)) {
            return false;
        }
        
        return false;
    }

    // Hàm định dạng ngày giờ
    private function formatDate($date)
    {
        try {
            //return Carbon::parse($date)->format('d/m/Y H:i:s');
            return Carbon::parse($date)
                ->setTimezone('Asia/Ho_Chi_Minh')->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            return $date; // Trả về nguyên gốc nếu không parse được
        }
    }

    public function transformCollection(Collection $items)
    {
        return $items->map(function ($item) {
            return $this->transform($item);
        })->toArray();
    }
}

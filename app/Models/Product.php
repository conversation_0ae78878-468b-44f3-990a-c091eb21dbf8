<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Log;
use Modules\PartnerManagement\Models\PMSProjectRef;

class Product extends Model
{
    protected $table = 'm_products';

    public $timestamps = false;
    protected $fillable = ['name', 'product_category_id', 'code', 'short_name', 'link_recommend', 'leader', 'type'];

    const KE_CODE = 'KE';
    const SIGN_TYPE_NEW = 1;
    const SIGN_TYPE_RENEW = 0;
    const SIGN_TYPE = [
        self::SIGN_TYPE_NEW => 'Ký mới',
        self::SIGN_TYPE_RENEW => 'Tái ký',
    ];

    public function contracts()
    {
        return $this->hasMany(Contract::class, 'product_id', 'id');
    }

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'product_category_id', 'id');
    }

    public function product_ranks()
    {
        return $this->hasMany(ProductRank::class, 'product_id', 'id')->orderBy('year', 'ASC');
    }
    public function sales()
    {
        return $this->hasMany(Contract::class, 'product_id', 'id');
    }

    public function leader()
    {
        return $this->belongsTo(User::class, 'leader', 'id');
    }

    public function tickets()
    {
        return $this->hasMany(Ticket::class, 'product_id', 'id');
    }

    public function ticketsByCustomerCare()
    {
        return $this->hasMany(Ticket::class, 'product_id', 'id')->where('source', Ticket::SOURCE_CALL_CENTER);
    }

    public function ticketsBySystem()
    {
        return $this->hasMany(Ticket::class, 'product_id', 'id')->where('source', Ticket::SOURCE_SYSTEM);
    }

    public function scopeActive($query)
    {
        return $query->where('show', 1);
    }

    public function scopeKidsEnglish($query)
    {
        return $query->where('code', self::KE_CODE);
    }

    public function scopeAvailableForPartner($query)
    {
        return $query->where('available_partner', 1);
    }

    /**
     * Get the PMS project references associated with the product.
     */
    public function pmsProjectRefs()
    {
        return $this->hasMany(PMSProjectRef::class, 'product_id', 'id');
    }

    /**
     * Lọc theo nhóm Category 
     */
    public function scopeByCategoryGroup($query, $code)
    {
        return $query->whereHas('category', function ($q) use ($code) {
            $q->where('group', $code);
        });
    }

    //query doanh thu theo từng sản phẩm
    // public static function sql()
    // {
    //     $sql = "SELECT p.id, p.code, p.short_name,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '01' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_1,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '02' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_2,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '03' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_3,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '04' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_4 ,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '05' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_5 ,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '06' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_6 ,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '07' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_7 ,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '08' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_8 ,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '09' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_9 ,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '10' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_10 ,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '11' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_11 ,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND MONTH(c.contract_signing_date) = '12' AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) sale_12
    //         FROM m_products p";
    //     return $sql;
    // }
    // public static function query_year()
    // {
    //     $sql = "SELECT p.id, p.code, p.short_name,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND YEAR(c.contract_signing_date) = YEAR(CURDATE())) current_year,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND YEAR(c.contract_signing_date) = YEAR(CURDATE()) - 1 ) last_year,
    //         (SELECT SUM(c.payment_amount) FROM contracts c WHERE c.product_id = p.id AND YEAR(c.contract_signing_date) = YEAR(CURDATE()) - 2 ) two_year_ago
    //         FROM m_products p WHERE p.show = 1";
    //     return $sql;
    // }


}

<?php

namespace App\Http\Controllers;

use App\Builder\NotificationBuilder;
use App\Builder\NotificationConfig;
use Illuminate\Http\Request;
use App\Models\InOutsOnline;
use App\Models\Response;
use App\Models\User;
use App\Models\UserWorking;
use App\Notifications\InOutOnlineNotification;
use App\Events\ConfirmInOutOnlineEvent;
use App\Events\ConfirmInOutEvent;
use App\Models\InOutExplanation;
use App\Notifications\InOutNotification;
use App\Providers\UtilityServiceProvider as u;
use App\Models\SystemConfig;
use App\Models\PersonInOut;
use Illuminate\Support\Facades\DB;
use App\Transformer\APIJsonResponse;
use App\Transformer\InOutsOnlineTransformer;
use App\Models\Position;
use Carbon\Carbon;
use App\Services\FirebaseNotification;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class InOutsOnlineController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $userId = $user->id;
        $isLeader = isset($user->user->position) && $user->user->position->is_leader ? $user->user->position->is_leader : null;
        $departmentId = isset($user->user->department) && $user->user->department->id ? $user->user->department->id : null;
        $roles = $user->getRoleNames();
        $roleName = $roles[0] ?: null;
        $month = $request->date;

        // get list data
        $model = InOutsOnline::likeMonth($month);
        if (auth()->user()->hasAnyPermission(['in_outs_online_management'])) {
            if ($isLeader) {
                $teamUserID = UserWorking::active()->department($request->department_id)->when($request->rank_code, function ($q, $rankCode) {
                    $q->whereHas('position', function ($qr) use ($rankCode) {
                        $qr->where('code', $rankCode);
                    });
                })->pluck('user_id')->toArray();
                $model->whereIn('user_id', $teamUserID);
            } else {
                $model->onlyMe($userId);
            }
        } else {
            if ($request->department_id) {
                $teamUserID = UserWorking::active()->department($request->department_id)->when($request->rank_code, function ($q, $rankCode) {
                    $q->whereHas('position', function ($qr) use ($rankCode) {
                        $qr->where('code', $rankCode);
                    });
                })->pluck('user_id')->toArray();
                $model->whereIn('user_id', $teamUserID);
            }
        }
        $data = $model->with('user.user.position', 'user.user.department', 'approve:id,name')->orderby('checkin', 'DESC')->paginate(20);
        return Response::formatResponse(config('apicode.SUCCESS'), $data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required',
            'note' => 'required',
        ]);
        try {
            $params = $request->all();
            $params['status'] = 1;
            $date = date('Y-m-d', strtotime($params['date']));
            $currentDate = Carbon::now()->format('Y-m-d');
            $params['checkin'] = Carbon::parse($currentDate)->eq($date) ? date('Y-m-d H:i') : $params['date'] . ' 08:00';
            $config = SystemConfig::getExplain();
            $explainOnline = InOutsOnline::onlyMe($params['user_id'])->likeMonth($params['month'])->get();
            $configInoutExplain = (int)$config[SystemConfig::EXPLAIN_ONLINE_ALLOW];
            if ($explainOnline && count($explainOnline) >= $configInoutExplain) {
                return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), null, 'Bạn đã vượt quá số lần checkin online!');
            }
            $check = $this->preAddCheckIn($params['checkin'], $params['user_id']); // kiểm tra xem đã có bản ghi checkin của hôm nay chưa để cảnh báo
            if ($check == 0) {
                InOutsOnline::create($params);

                # bắn notify cho người duyệt
                $managerId = u::getCurrentManager(false);
                $confirmUserId = $managerId ?: $params['user_id'];
                $userNoti = User::find($confirmUserId);
                $message = [
                    'title' => 'Duyệt đăng ký làm việc online cho nhân viên: ' . $request->user()->name,
                    'link' => '/checkin-checkout-online',
                    'content' => 'Bạn có thông báo duyệt đăng ký làm việc online cho nhân viên: ' . $request->user()->name,
                    'option' => 'add',
                    'type' => '4',
                ];

                $config = new NotificationConfig();
                $config->setUser($userNoti);
                $config->setMsg($message);
                $config->setNotification(function () use ($message) {
                    return new InOutOnlineNotification($message);
                });
                $config->setBroadcast(function () use ($userNoti) {
                    $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\InOutOnlineNotification")->first();
                    return new ConfirmInOutOnlineEvent($userNoti, $newMessage);
                });
                $config->setNotificationType(FirebaseNotification::PUSH_TYPE_IN_OUT_ONLINE);


                NotificationBuilder::getBuilder()
                    ->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT]);

                return Response::formatResponse(config('apicode.SUCCESS'), $params);
            } else {
                return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), null, 'Bạn đã checkIn hôm nay rồi, vui lòng chờ đến giờ checkOut!');
            }
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\InOutsOnline $inOutsOnline
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, InOutsOnline $inOutsOnline)
    {
        try {
            $params = $request->all();
            $inOutOnline = InOutsOnline::where('id', $request->inOutsOnline['id'])->first();
            $date = Carbon::createFromFormat('Y-m-d H:i:s', $inOutOnline->checkin)->format('Y-m-d');
            $currentDate = Carbon::now()->format('Y-m-d');
            $params['checkout'] = $currentDate === $date ? date('Y-m-d H:i') : $date . ' 17:00';
            $inOutsOnline->update($params);
            return Response::formatResponse(config('apicode.SUCCESS'), $params);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\InOutsOnline $inOutsOnline
     * @return \Illuminate\Http\Response
     */
    public function destroy(InOutsOnline $inOutsOnline)
    {
        $inOutsOnline->delete();
        return Response::formatResponse(config('apicode.SUCCESS'), $inOutsOnline);
    }

    public function preAddCheckIn($checkin, $user_id)
    {
        $checkin = date_format(date_create($checkin), 'Y-m-d');
        $check = InOutsOnline::where('user_id', $user_id)->whereRaw("checkin like '{$checkin}%'")->count();
        return $check;
    }

    /**
     * Update approve status the specified resource from storage.
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\InOutsOnline $inOutsOnline
     * @return \Illuminate\Http\Response
     */
    public function approve(Request $request, InOutsOnline $inOutsOnline)
    {
        $userId = $request->user()->id;
        $inOutsOnline->update(['status' => 2, 'approve_id' => $userId, 'approve_time' => Carbon::now()]);

        # bắn email cho khối nhân sự
        $model = User::select('name')->where('id', $inOutsOnline->user_id)->first();
        $rs = SystemConfig::select('content')->notificationHR()->first();
        $receiveEmails = explode(',', $rs->content);
        $contentMails = [];
        foreach ($receiveEmails as $email) {
            $user = User::select('name')->where('email', $email)->first();
            $contentMail = (object)[];
            $contentMail->subject = "[VIETEC-QLDN]- Thông báo Xác nhận đăng ký làm việc Online của nhân sự: " . $model->name;
            $contentMail->receiver = $email;
            $contentMail->receiver_name = ($user && $user->name) ? $user->name : 'Quý Anh/chị';
            $contentMail->message = "Nhân sự {$model->name} đã được cấp quản lý duyệt đăng ký làm việc Online ngày " . vietec_format_date($inOutsOnline->checkin) . ". Người duyệt: " . auth()->user()->name . ".";
            $contentMails[] = $contentMail;
        }
        if (!isArrEmptyOrNull($contentMails)) {
            $config = new NotificationConfig();
            $config->setContentMails($contentMails);
            NotificationBuilder::getBuilder()
                ->sendMessage($config, [NotificationBuilder::EMAIL]);
        }
        return Response::formatResponse(config('apicode.SUCCESS'), $inOutsOnline);
    }

    /**
     * Update reject status the specified resource from storage.
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\InOutsOnline $inOutsOnline
     * @return \Illuminate\Http\Response
     */
    public function reject(Request $request, InOutsOnline $inOutsOnline)
    {
        $userId = $request->user()->id;
        $inOutsOnline->update(['status' => 3, 'approve_id' => $userId]);
        return Response::formatResponse(config('apicode.SUCCESS'), $inOutsOnline);
    }

    public function rollback(Request $request, InOutsOnline $inOutsOnline)
    {
        $inOutsOnline->update(['status' => 1, 'approve_id' => NULL]);
        return Response::formatResponse(config('apicode.SUCCESS'), $inOutsOnline);
    }

    public function checkinCheckoutExplain(Request $request)
    {
        $request->validate([
            'user' => 'required',
            'date' => 'required',
            'checkin' => 'required',
            'checkout' => 'required|after:checkin',
            'note' => 'required',
        ], [], [
            'user' => 'Họ tên',
            'date' => 'Ngày',
            'checkin' => 'Checkin',
            'checkout' => 'Checkout',
            'note' => 'Ghi chú',
        ]);
        try {
            DB::beginTransaction();
            $data = InOutExplanation::where('user_id', $request->user['id'])->where('date', $request->date)->first();
            if ($data) {
                return response()->json(['message' => 'Giải trình đã tồn tại'], 400);
            }
            $user = User::where('id', $request->user['id'])->with('hanet')->first();
            $month = Carbon::parse($request->date)->format('Y-m');
            $config = SystemConfig::getExplain();
            $explainMiss = InOutExplanation::explainMiss($user['id'], $month, $request->rule_status)->get();
            $configInoutExplain = $request->rule_status ? (int)$config[SystemConfig::EXPLAIN_ALLOW_MISS] : (int)$config[SystemConfig::EXPLAIN_ALLOW];
            if ($explainMiss && count($explainMiss) >= $configInoutExplain) {
                return response()->json(['message' => 'Bạn đã vượt quá số lần giải trình chấm công!'], 400);
            }
            //thêm mới checkin
            $rowPersonInOut = new PersonInOut();
            $rowPersonInOut->date = $request->date . ' ' . $request->checkin;
            $rowPersonInOut->personTitle = $user['hanet']['title'];
            $rowPersonInOut->action_type = 'update';
            $rowPersonInOut->detected_image_url = null;
            $rowPersonInOut->placeID = '10818';
            $rowPersonInOut->deviceID = 'C21024B545';
            $rowPersonInOut->personName = $user['hanet']['name'];
            $rowPersonInOut->aliasID = $user['hanet']['aliasID'];
            $rowPersonInOut->data_type = 'log';
            $rowPersonInOut->personID = $user['hanet']['personID'];
            $rowPersonInOut->time = strtotime($request->date . ' ' . $request->checkin) * 1000;
            $rowPersonInOut->personType = 0;
            $rowPersonInOut->placeName = 'Công ty Vietec';
            $rowPersonInOut->hash = 'hash';
            $rowPersonInOut->mask = -1;
            $rowPersonInOut->deviceName = 'VP Vietec tầng 4';
            $rowPersonInOut->create_by = 1;
            $rowPersonInOut->save();

            //thêm mới checkout
            $rowPersonInOut = new PersonInOut();
            $rowPersonInOut->date = $request->date . ' ' . $request->checkout;
            $rowPersonInOut->personTitle = $user['hanet']['title'];
            $rowPersonInOut->action_type = 'update';
            $rowPersonInOut->detected_image_url = null;
            $rowPersonInOut->placeID = '10818';
            $rowPersonInOut->deviceID = 'C21024B545';
            $rowPersonInOut->personName = $user['hanet']['name'];
            $rowPersonInOut->aliasID = $user['hanet']['aliasID'];
            $rowPersonInOut->data_type = 'log';
            $rowPersonInOut->personID = $user['hanet']['personID'];
            $rowPersonInOut->time = strtotime($request->date . ' ' . $request->checkout) * 1000;
            $rowPersonInOut->personType = 0;
            $rowPersonInOut->placeName = 'Công ty Vietec';
            $rowPersonInOut->hash = 'hash';
            $rowPersonInOut->mask = -1;
            $rowPersonInOut->deviceName = 'VP Vietec tầng 4';
            $rowPersonInOut->create_by = 1;
            $rowPersonInOut->save();

            //lưu vào in_out_explanation
            $model = new InOutExplanation();
            $model->user_id = $request->user['id'];
            $model->date = $request->date;
            $model->content = $request->note;
            $model->status = 1;
            $model->rule_status = $request->rule_status;
            // $model->confirm_by = auth()->user()->id;
            $model->save();
            # bắn notify cho người duyệt
            $managerId = u::getCurrentManager(false);
            $confirmUserId = $managerId ?: $request->user['id'];
            $userNoti = User::find($confirmUserId);
            $message = [
                'title' => 'Duyệt giải trình chấm công cho nhân viên: ' . $request->user()->name,
                'link' => '/checkin-checkout',
                'content' => 'Bạn có thông báo duyệt giải trình chấm công cho nhân viên: ' . $request->user()->name,
                'option' => 'add',
                'type' => '3',
            ];

            $config = new NotificationConfig();
            $config->setUser($userNoti);
            $config->setMsg($message);
            $config->setNotification(function () use ($message) {
                return new InOutNotification($message);
            });
            $config->setBroadcast(function () use ($userNoti) {
                $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\InOutNotification")->first();
                return new ConfirmInOutEvent($userNoti, $newMessage);
            });
            $config->setNotificationType(FirebaseNotification::PUSH_TYPE_IN_OUT_EXPLANATION);

            NotificationBuilder::getBuilder()
                ->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT]);
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $model);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('InOutsOnlineController checkinCheckoutExplain: ' . $e->getMessage());
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function listMobile(Request $request)
    {
        try {
            $user = $request->user();
            $userId = $user->id;
            $isLeader = isset($user->user->position) && $user->user->position->is_leader ? $user->user->position->is_leader : null;
            $departmentId = isset($user->user->department) && $user->user->department->id ? $user->user->department->id : null;
            $roles = $user->getRoleNames();
            $roleName = $roles[0] ?: null;
            //$month = $request->date;
            $month = $request->month;

            // get list data
            $model = InOutsOnline::likeMonth($month);

            if ($roleName != 'admin' && $roleName != 'hr') {
                if ($isLeader) {
                    $teamUserID = UserWorking::active()->department($departmentId)->when($request->rank_code, function ($q, $rankCode) {
                        $q->whereHas('position', function ($qr) use ($rankCode) {
                            $qr->where('code', $rankCode);
                        });
                    })->pluck('user_id')->toArray();
                    $model->whereIn('user_id', $teamUserID);
                } else {
                    $model->onlyMe($userId);
                }
            } else {
                if ($request->department_id) {
                    $teamUserID = UserWorking::active()->department($request->department_id)->when($request->rank_code, function ($q, $rankCode) {
                        $q->whereHas('position', function ($qr) use ($rankCode) {
                            $qr->where('code', $rankCode);
                        });
                    })->pluck('user_id')->toArray();
                    $model->whereIn('user_id', $teamUserID);
                }
            }
            $data = $model->with('user.user.position', 'user.user.department', 'approve:id,name')->orderby('checkin', 'DESC')->get();
            foreach ($data as $item) {
                $item->show_btn_approve = $this->checkApproveReject($item);
                $item->show_btn_rollback = $this->checkRollback($item);
                $item->show_btn_delete = $item['status'] == 1 && auth()->user()->id == $item['user_id'];
                $item->show_btn_checkout = !$this->showButtonCheckOut($item) && auth()->user()->id == $item['user_id'];
            }
            $rs = (new APIJsonResponse)->responseSuccess((new InOutsOnlineTransformer)->transforms($data));
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function checkApproveReject($item)
    {
        $checkinDate = date('Y-m', strtotime($item['checkin']));
        $currentDate = date('Y-m');
        return $checkinDate === $currentDate && $item['status'] == 1;
    }

    public function checkRollback($item)
    {
        $checkinDate = date('Y-m', strtotime($item['checkin']));
        $currentDate = date('Y-m');
        return $checkinDate === $currentDate && $item['status'] != 1;
    }

    public function showButtonCheckOut($item)
    {
        if ($item['checkout'] === '0000-00-00 00:00:00' || $item['checkout'] === '0000-00-00' || empty($item['checkout'])) {
            return false;
        }
        return $item['checkout'] && (date('Y-m-d H:i:s', strtotime($item['checkout'])) === $item['checkout'] || date('Y-m-d', strtotime($item['checkout'])) === $item['checkout']);
    }

    public function viewAddMobile(Request $request)
    {
        try {
            $user = auth()->user();
            $staff = UserWorking::active()->where('user_id', $user->id)->first();

            $manager = UserWorking::allActive()
                ->where('position_id', $request->user()->user->leader->id)->with('user')
                ->first();

            $position = Position::year(date('Y'))->select('id', 'name', 'is_leader', 'department_id')->whereId($staff->position_id)->first();

            $arr_view_data = [
                "user_id"                   => $user->id,
                "staff_code"                => $user->staff_code,
                "user_name"                 => $user->name,
                "position"                  => $position->name,
                "position_id"               => $staff->position_id,
                "manager"                   => $manager->user->name
            ];

            $rs = (new APIJsonResponse)->responseSuccess($arr_view_data);
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function addMobile(Request $request)
    {

        $request->validate([
            'user_id' => 'required',
            'note' => 'required',
        ]);
        try {
            $params = $request->all();
            $params['status'] = 1;
            $date = Carbon::createFromFormat('Y-m-d', $params['date']);
            $params['checkin'] = Carbon::now()->eq($date) ? date('Y-m-d H:i') : $params['date'] . ' 08:00';
            $config = SystemConfig::getExplain();
            $month = date('Y-m', strtotime($params['date']));
            $explainOnline = InOutsOnline::onlyMe($params['user_id'])->likeMonth($month)->get();
            $configInoutExplain = (int)$config[SystemConfig::EXPLAIN_ONLINE_ALLOW];
            if ($explainOnline && count($explainOnline) >= $configInoutExplain) {
                return response()->json(['message' => 'Bạn đã vượt quá số lần checkin online!'], 400);
            }
            $check = $this->preAddCheckIn($params['checkin'], $params['user_id']); // kiểm tra xem đã có bản ghi checkin của hôm nay chưa để cảnh báo
            if ($check == 0) {
                InOutsOnline::create($params);

                # bắn notify cho người duyệt
                $managerId = u::getCurrentManager(false);
                $confirmUserId = $managerId ?: $params['user_id'];
                $userNoti = User::find($confirmUserId);
                $message = [
                    'title' => 'Duyệt đăng ký làm việc online cho nhân viên: ' . $request->user()->name,
                    'link' => '/checkin-checkout-online',
                    'content' => 'Bạn có thông báo duyệt đăng ký làm việc online cho nhân viên: ' . $request->user()->name,
                    'option' => 'add',
                    'type' => '4',
                ];

                //set config notification
                $config = new NotificationConfig();
                $config->setUser($userNoti);
                $config->setMsg($message);
                $config->setNotification(function () use ($message) {
                    return new InOutOnlineNotification($message);
                });
                $config->setBroadcast(function () use ($userNoti) {
                    $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\InOutOnlineNotification")->first();
                    return new ConfirmInOutOnlineEvent($userNoti, $newMessage);
                });
                $config->setNotificationType(FirebaseNotification::PUSH_TYPE_IN_OUT_ONLINE);


                NotificationBuilder::getBuilder()
                    ->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT]);

                $rs = (new APIJsonResponse)->responseSuccess($params);
            } else {
                return response()->json(['description' => 'Bạn đã checkIn hôm nay rồi, vui lòng chờ đến giờ checkOut!'], 400);
            }
        } catch (\Exception $e) {
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function destroyMobile(Request $request)
    {
        try {
            $id = $request->id;
            DB::beginTransaction();
            $data = InOutsOnline::where('id', $id)->delete();
            DB::commit();
            $rs = (new APIJsonResponse)->responseSuccess($data);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('AbsenceLetterController destroy: ' . $e->getMessage());
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function updateMobile(Request $request)
    {
        try {
            $id = $request->id;
            DB::beginTransaction();
            $params = $request->all();
            $inOutOnline = InOutsOnline::where('id', $id)->first();
            $date = Carbon::createFromFormat('Y-m-d H:i:s', $inOutOnline->checkin)->format('Y-m-d');
            $currentDate = Carbon::now()->format('Y-m-d');
            $params['checkout'] = $currentDate === $date ? date('Y-m-d H:i') : $date . ' 17:00';
            InOutsOnline::where('id', $id)->update($params);
            DB::commit();
            $rs = (new APIJsonResponse)->responseSuccess($params);
        } catch (\Exception $e) {
            DB::rollback();
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function approveMobile(Request $request)
    {
        try {
            DB::beginTransaction();
            $id = $request->id;
            $userId = $request->user()->id;
            $data = InOutsOnline::where('id', $id)->get()->first();
            $data->update(['status' => 2, 'approve_id' => $userId, 'approve_time' => Carbon::now()]);

            # bắn email cho khối nhân sự
            $model = User::select('name')->where('id', $data->user_id)->first();
            $rs = SystemConfig::select('content')->notificationHR()->first();
            $receiveEmails = explode(',', $rs->content);
            $contentMails = [];
            foreach ($receiveEmails as $email) {
                $user = User::select('name')->where('email', $email)->first();
                $contentMail = (object)[];
                $contentMail->subject = "[VIETEC-QLDN]- Thông báo Xác nhận đăng ký làm việc Online của nhân sự: " . $model->name;
                $contentMail->receiver = $email;
                $contentMail->receiver_name = ($user && $user->name) ? $user->name : 'Quý Anh/chị';
                $contentMail->message = "Nhân sự {$model->name} đã được cấp quản lý duyệt đăng ký làm việc Online ngày " . vietec_format_date($data->checkin) . ". Người duyệt: " . auth()->user()->name . ".";
                $contentMails[] = $contentMail;
            }
            if (!isArrEmptyOrNull($contentMails)) {
                $config = new NotificationConfig();
                $config->setContentMails($contentMails);
                NotificationBuilder::getBuilder()
                    ->sendMessage($config, [NotificationBuilder::EMAIL]);
            }
            DB::commit();
            $rs = (new APIJsonResponse)->responseSuccess($data);
        } catch (\Exception $e) {
            DB::rollback();
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function rejectMobile(Request $request)
    {
        try {
            DB::beginTransaction();
            $id = $request->id;
            $userId = $request->user()->id;

            InOutsOnline::where('id', $id)->update(['status' => 3, 'approve_id' =>  $userId]);
            DB::commit();
            $rs = (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            DB::rollback();
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function rollbackMobile(Request $request)
    {
        try {
            DB::beginTransaction();
            $id = $request->id;
            $userId = $request->user()->id;

            InOutsOnline::where('id', $id)->update(['status' => 1, 'approve_id' =>  $userId]);
            DB::commit();
            $rs = (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            DB::rollback();
            $rs = (new APIJsonResponse)->responseError();
        }

        return $rs;
    }

    public function checkinCheckoutExplainMobile(Request $request)
    {
        try {
            $messages = [
                'user_id' => 'Họ tên không được bỏ trống',
                'date' => 'Ngày không được bỏ trống',
                'checkin' => 'Checkin không được bỏ trống',
                'checkout' => 'Checkout không được bỏ trống',
                'note' => 'Ghi chú không được bỏ trống',
            ];
            $validator = Validator::make($request->all(), [
                'user_id' => 'required',
                'date' => 'required',
                'checkin' => 'required',
                'checkout' => 'required|after:checkin',
                'note' => 'required',
            ], $messages);
            if ($validator->fails()) {
                return (new APIJsonResponse)->responseError(401, $validator->errors()->first(), null);
            }
            DB::beginTransaction();
            $data = InOutExplanation::where('user_id', $request->user_id)->where('date', $request->date)->first();
            if ($data) {
                $errors = 'Giải trình đã tồn tại!';
                return (new APIJsonResponse)->responseError(400, $errors, null, null);
            }
            $user = User::where('id', $request->user_id)->with('hanet')->first();
            $month = Carbon::parse($request->date)->format('Y-m');
            $config = SystemConfig::getExplain();
            $date = date('Y-m-d');
            $rule_status = $request->date == $date ? 0 : 1;

            $explainMiss = InOutExplanation::explainMiss($user['id'], $month, $rule_status)->get();

            $configInoutExplain = $rule_status ? (int)$config[SystemConfig::EXPLAIN_ALLOW_MISS] : (int)$config[SystemConfig::EXPLAIN_ALLOW];
            if ($explainMiss && count($explainMiss) >= $configInoutExplain) {
                $errors = 'Bạn đã vượt quá số lần giải trình chấm công!';
                return (new APIJsonResponse)->responseError(400, $errors, null, null);
            }
            //thêm mới checkin
            $rowPersonInOut = new PersonInOut();
            $rowPersonInOut->date = $request->date . ' ' . $request->checkin;
            $rowPersonInOut->personTitle = $user['hanet']['title'];
            $rowPersonInOut->action_type = 'update';
            $rowPersonInOut->detected_image_url = null;
            $rowPersonInOut->placeID = '10818';
            $rowPersonInOut->deviceID = 'C21024B545';
            $rowPersonInOut->personName = $user['hanet']['name'];
            $rowPersonInOut->aliasID = $user['hanet']['aliasID'];
            $rowPersonInOut->data_type = 'log';
            $rowPersonInOut->personID = $user['hanet']['personID'];
            $rowPersonInOut->time = strtotime($request->date . ' ' . $request->checkin) * 1000;
            $rowPersonInOut->personType = 0;
            $rowPersonInOut->placeName = 'Công ty Vietec';
            $rowPersonInOut->hash = 'hash';
            $rowPersonInOut->mask = -1;
            $rowPersonInOut->deviceName = 'VP Vietec tầng 4';
            $rowPersonInOut->create_by = 1;
            $rowPersonInOut->save();

            //thêm mới checkout
            $rowPersonInOut = new PersonInOut();
            $rowPersonInOut->date = $request->date . ' ' . $request->checkout;
            $rowPersonInOut->personTitle = $user['hanet']['title'];
            $rowPersonInOut->action_type = 'update';
            $rowPersonInOut->detected_image_url = null;
            $rowPersonInOut->placeID = '10818';
            $rowPersonInOut->deviceID = 'C21024B545';
            $rowPersonInOut->personName = $user['hanet']['name'];
            $rowPersonInOut->aliasID = $user['hanet']['aliasID'];
            $rowPersonInOut->data_type = 'log';
            $rowPersonInOut->personID = $user['hanet']['personID'];
            $rowPersonInOut->time = strtotime($request->date . ' ' . $request->checkout) * 1000;
            $rowPersonInOut->personType = 0;
            $rowPersonInOut->placeName = 'Công ty Vietec';
            $rowPersonInOut->hash = 'hash';
            $rowPersonInOut->mask = -1;
            $rowPersonInOut->deviceName = 'VP Vietec tầng 4';
            $rowPersonInOut->create_by = 1;
            $rowPersonInOut->save();

            //lưu vào in_out_explanation
            $model = new InOutExplanation();
            $model->user_id = $request->user_id;
            $model->date = $request->date;
            $model->content = $request->note;
            $model->status = 1;
            $model->rule_status = $rule_status;
            $model->save();
            # bắn notify cho người duyệt
            $managerId = u::getCurrentManager(false);
            $confirmUserId = $managerId ?: $request->user_id;
            $userNoti = User::find($confirmUserId);
            $message = [
                'title' => 'Duyệt giải trình chấm công cho nhân viên: ' . $request->user()->name,
                'link' => '/checkin-checkout',
                'content' => 'Bạn có thông báo duyệt giải trình chấm công cho nhân viên: ' . $request->user()->name,
                'option' => 'add',
                'type' => '3',
            ];
            $config = new NotificationConfig();
            $config->setUser($userNoti);
            $config->setMsg($message);
            $config->setNotification(function () use ($message) {
                return new InOutNotification($message);
            });
            $config->setBroadcast(function () use ($userNoti) {
                $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\InOutNotification")->first();
                return new ConfirmInOutEvent($userNoti, $newMessage);
            });
            $config->setNotificationType(FirebaseNotification::PUSH_TYPE_IN_OUT_EXPLANATION);

            NotificationBuilder::getBuilder()
                ->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT]);
            DB::commit();
            return (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            DB::rollBack();
            return (new APIJsonResponse)->responseError();
        }
    }

    public function HRCreateRecord(Request $request)
    {
        $request->validate([
            'user_id' => 'required',
            'from_date' => 'required',
            'to_date' => 'required',
            'timekeeping' => 'required',
            'timeWork' => 'required'
        ]);
        try {
            $save_db = [];
            $params = $request->all();
            $save_db['status'] = 2; //1 là chờ duyệt, 2 là đã duyệt
            $save_db['user_id'] = $params['user_id'];
            if ($params['timeWork'] == 2) {
                $save_db['checkin'] =  date('Y-m-d', strtotime($params['from_date'])) . ' 13:00';
                $save_db['checkout'] =  date('Y-m-d', strtotime($params['to_date'])) . ' 17:00';
            } else {
                if ($params['timeWork'] == 1) {
                    $save_db['checkin'] =  date('Y-m-d', strtotime($params['from_date'])) . ' 08:00';
                    $save_db['checkout'] =  date('Y-m-d', strtotime($params['to_date'])) . ' 12:00';
                } else {
                    $save_db['checkin'] =  date('Y-m-d', strtotime($params['from_date'])) . ' 08:00';
                    $save_db['checkout'] =  date('Y-m-d', strtotime($params['to_date'])) . ' 17:00';
                }
            }
            $save_db['note'] = $params['noteContent'];

            InOutsOnline::create($save_db);
            return Response::formatResponse(config('apicode.SUCCESS'), $save_db);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }
}

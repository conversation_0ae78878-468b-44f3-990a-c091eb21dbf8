<?php

namespace App\Http\Controllers;

use App\Http\Requests\ContractStoreRequest;
use App\Http\Requests\ContractUpdateRequest;
use App\Models\Client;
use App\Models\Contract;
use App\Models\ContractPaymentHistory;
use App\Models\KPISummaryUpgrade;
use App\Models\Dashboard;
use App\Models\Department;
use App\Models\KEContract;
use App\Models\Product;
use Illuminate\Http\Request;
use App\Models\Response;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\ExportContractStatus;
use App\Models\ImportContract;
use App\Models\KEContractStatusHistory;
use App\Models\Position;
use App\Models\TmpContract;
use App\Models\UserWorking;
use App\Models\ProvinceBusinessMarket;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Notifications\EmailNotification;
use PhpOffice\PhpSpreadsheet\Style;
use App\Transformer\APIJsonResponse;
use App\Transformer\ContractTransformer;
use App\Transformer\TmpContractTransformer;
use App\Transformer\ProvinceBusinessTransformer;
use Illuminate\Support\Facades\Validator;
use App\Models\TeamSaleArea;

class ContractController extends Controller
{
    public function getCompany()
    {
        $data = [];
        foreach (SystemConfig::COMPANY as $id => $name) {
            $data[] = [
                'id' => $id,
                'name' => $name
            ];
        }
        return response()->json($data);
    }

    public function index(Request $request)
    {
        try {
            $perPage = $request->per_page ?? 20;
            $query  = Contract::with('clients.provinceBusinessMarket', 'clients.districtBusinessMarket', 'product', 'position', 'creator', 'paymentHistory', 'feedbackHistory');
            $this->dividePermission($query);
            $this->getCondition($request, $query);
            $sum_rs = with(clone $query)->get();
            $rs = with(clone $query)->orderBy('created_at', 'DESC')->paginate($perPage);
            $sum_contract_value = $sum_rs->sum('contract_value');
            $sum_received_money = $sum_rs->filter(function ($item) {
                return is_numeric($item->received_money);
            })->sum('received_money');

            $data = [
                'data' => $rs,
                'sum_contract_value' => $sum_contract_value,
                'sum_received_money' => $sum_received_money,
                'sum_debt' => $sum_contract_value -  $sum_received_money,
            ];
            return response()->json(['data' => $data]);
        } catch (Exception $e) {
            Log::error('ContractController index: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function paymentList(Request $request)
    {
        try {
            $sum_contract_value = 0;
            $sum_received_money = 0;
            if ($request->province_id) {
                $query  = Contract::whereNotNull('debt')->where('debt', '!=', 0)->with('clients', 'product', 'position', 'creator', 'paymentHistory', 'feedbackHistory');
                $this->dividePermission($query);
                $this->getCondition($request, $query);
                $sum_rs = with(clone $query)->get();
                $rs = with(clone $query)->orderBy('created_at', 'DESC')->paginate(10);
                $sum_contract_value = $sum_rs->sum('contract_value');
                $sum_received_money = $sum_rs->filter(function ($item) {
                    return is_numeric($item->received_money);
                })->sum('received_money');
            } else {
                $rs = [];
            }

            $data = [
                'data' => $rs,
                'sum_contract_value' => $sum_contract_value,
                'sum_received_money' => $sum_received_money,
                'sum_debt' => $sum_contract_value -  $sum_received_money,
            ];
            return response()->json(['data' => $data]);
        } catch (Exception $e) {
            Log::error('ContractController paymentList: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function paymentSave(Request $request)
    {
        try {
            DB::beginTransaction();
            $errors = [];
            $count = [];
            foreach ($request->payment_lists as $key => $item) {
                $e = (object) $item;
                //cập nhật bảng contracts
                if (!isset($e->payment_total) && !isset($e->payment_date)) {
                    continue;
                }

                if (!isset($e->payment_total) && isset($e->payment_date)) {
                    $errors[] = 'Số tiền thanh toán dòng ' . $key + 1 . ' không được bỏ trống';
                }

                if (isset($e->payment_total) && !isset($e->payment_date)) {
                    $errors[] = 'Ngày thanh toán dòng ' . $key + 1 . ' không được bỏ trống';
                }
                if (isset($e->payment_total) && isset($e->payment_date)) {
                    $query = Contract::where('id', $e->id);
                    $tmpContract = $query->first();
                    $query->update(['received_money' => $e->payment_total + $e->received_money, 'debt' => $e->contract_value - $e->payment_total - $e->received_money]);
                    $e->sale_real_date = isset($e->sale_real_date) ? $e->sale_real_date : $e->payment_date;
                    // thêm lịch sử thanh toán
                    $payment_history = new ContractPaymentHistory();
                    $payment_history->contract_id = $e->id;
                    $payment_history->contract_code = $e->contract_code;
                    $payment_history->current_contract_value = $e->contract_value;
                    $payment_history->payment_amount = $e->payment_amount;
                    $payment_history->current_received_money = $e->payment_total + $e->received_money;
                    $payment_history->current_debt = $e->contract_value - $e->payment_total - $e->received_money;
                    $payment_history->payment_date = $e->payment_date;
                    $payment_history->sale_real_date = $e->sale_real_date;
                    $payment_history->created_by = auth()->user()->id;
                    $payment_history->type = ContractPaymentHistory::BASE_PRODUCT;
                    $payment_history->province_id = $tmpContract->province_id;
                    $payment_history->district_id = $tmpContract->district_id;
                    $payment_history->team = $tmpContract->team;
                    $payment_history->product_id = $tmpContract->product_id;
                    $payment_history->save();
                    array_push($count, $key);
                }
            }

            if (count($errors) > 0) {
                return response()->json(['errors' => $errors], 422);
            }
            DB::commit();
            return response()->json(['filter' => $request->filter, 'message' => 'Thêm thanh toán thành công ' . count($count) . ' hợp đồng!'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ContractController paymentSave: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function store(ContractStoreRequest $request)
    {
        DB::beginTransaction();
        try {
            $position_leader = Position::year(date('Y'))->select('id', 'representatives')->where('representatives', $request->team)->first();
            $leader = UserWorking::where('status', true)->where('position_id', $position_leader->id)->first();
            $staff = User::where('staff_code', $request->staff_code)->first();
            $product = Product::where('id', $request->product_id)->first();
            //ktra xem khách hàng đã có chưa
            $client = Client::searchClient($request->province_id, $request->district_id, $request->client_code);
            if (!$client) {
                Client::store($request);
                $client_code = $request->client_code;
            } else {
                $client_code = $client->code;
                Client::updateClient($request, $client->id);
            }
            $model = new TmpContract();
            TmpContract::store($request, $model, $position_leader->id, $leader->user_id, $staff->id, $client_code, $product->product_category_id);
            $model->save();
            DB::commit();
            return response()->json(['message' => 'Create Success'], 200);
        } catch (\Exception $e) {
            Log::error('ContractController store: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            $contract = Contract::find($id);
            if (!$contract) {
                return response()->json(['message' => 'Không tìm thấy hợp đồng!'], 404);
            }
            if (auth()->user()->id != $contract->creator_id) {
                return response()->json(['message' => 'Bạn không có quyền sửa bản ghi này!'], 400);
            }
            DB::beginTransaction();
            $contract->from_date = $request->from_date;
            $contract->to_date = $request->to_date;
            $contract->user_name_product = $request->user_name_product;
            $contract->invoice_status = $request->invoice_status;
            $contract->invoice_name = $request->invoice_name;
            $contract->invoice_address = $request->invoice_address;
            $contract->fb_school = $request->fb_school / 100;
            $contract->fb_district = $request->fb_district / 100;
            $contract->fb_province = $request->fb_province / 100;
            $contract->save();
            DB::commit();
            return response()->json(['message' => 'Create Success'], 200);
        } catch (\Exception $e) {
            Log::error('ContractController update: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function showTmpContract($tmpContractId)
    {
        try {
            $tmp_contract = TmpContract::where('id', $tmpContractId)->first();
            return $tmp_contract;
        } catch (\Exception $e) {
            Log::error('ContractController store: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function editTmpContract(ContractUpdateRequest $request, $tmpContractId)
    {
        try {
            DB::beginTransaction();
            $contract = TmpContract::find($tmpContractId);
            if (!$contract) {
                return response()->json(['message' => 'Không tìm thấy hợp đồng!'], 404);
            }
            if (auth()->user()->id != $contract->created_by) {
                return response()->json(['message' => 'Bạn không có quyền sửa bản ghi này!'], 400);
            }
            TmpContract::store($request, $contract, $request->position_leader_id, $request->leader_id, $request->sale_id, $request->client_code, $request->product_category_id);
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), 200);
        } catch (\Exception $e) {
            Log::error('ContractController editTmpContract: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function contractFormList(Request $request)
    {
        try {
            $query = TmpContract::where('save_type', ImportContract::FORM_SAVE)->whereIn('status', [ImportContract::WAIT_STATUS, ImportContract::REJECT_STATUS])
                ->when($request->province_id, function ($subQuery, $province_id) {
                    return $subQuery->where('province_id', $province_id);
                })->when($request->district_id, function ($subQuery, $district_id) {
                    return $subQuery->where('district_id', $district_id);
                })->when($request->product_id, function ($subQuery, $product_id) {
                    return $subQuery->where('product_id', $product_id);
                })->when($request->team, function ($subQuery, $team) {
                    return $subQuery->where('team', $team);
                })->when($request->company, function ($subQuery, $company) {
                    return $subQuery->where('company', $company);
                })->when($request->status, function ($subQuery, $status) {
                    return $subQuery->where('status', $status);
                })->when($request->year, function ($subQuery, $year) {
                    return $subQuery->whereYear('created_at', $year);
                });
            if (auth()->user()->hasAnyPermission(['review_contract'])) {
                $query;
            } else {
                $userIds = [auth()->user()->id];
                $child = KPISummaryUpgrade::getAllUser(KPISummaryUpgrade::getAllPosition());
                $userIds = array_merge($userIds, $child);
                $query->whereIn('created_by', $userIds);
            }
            $data = $query->with('createdBy', 'confirm_by', 'product', 'sale', 'province', 'district')->paginate(50);

            return Response::formatResponse(config('apicode.SUCCESS'), ['data' => $data]);
        } catch (\Exception $e) {
            Log::error('ContractController contractFormList: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function approveContractForm(Request $request)
    {
        DB::beginTransaction();
        try {
            if (!auth()->user()->hasAnyPermission(['review_contract'])) {
                return response()->json(['message' => 'Bạn không có quyền thực hiện hành động này này!'], 403);
            }
            $query = TmpContract::whereIn('id', $request->contract_ids)->where('confirm_by', null)->where('status', ImportContract::WAIT_STATUS);
            $tmp_contracts = $query->get();

            if (count($tmp_contracts) > 0) {
                $query->update(['status' => ImportContract::APPROVE_STATUS, 'confirm_by' => auth()->user()->id]);
            } else {
                return response()->json(['message' => 'Hợp đồng được duyệt không hợp lệ!'], 404);
            }
            foreach ($tmp_contracts as $e) {
                $client = Client::searchClient($e->province_id, $e->district_id, $e->client_code);
                Contract::store($e, $client->id, $e->created_by, null);
            }
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công!'], 200);
        } catch (\Exception $e) {
            Log::error('ContractController approveContractForm: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function rejectContractForm(Request $request)
    {
        try {
            if (!auth()->user()->hasAnyPermission(['review_contract'])) {
                return response()->json(['message' => 'Bạn không có quyền thực hiện hành động này!'], 403);
            }
            DB::beginTransaction();
            TmpContract::whereIn('id', $request->contract_ids)->update(['status' => ImportContract::REJECT_STATUS, 'confirm_by' => auth()->user()->id, 'reason_refuse' => $request->reason_refuse]);
            DB::commit();
            return response()->json(['message' => 'Xử lý thành công!'], 200);
        } catch (\Exception $e) {
            Log::error('ContractController rejectContractForm: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function deleteContractForm(Request $request)
    {
        try {
            DB::beginTransaction();
            $query = TmpContract::whereIn('id', $request->contract_ids);
            $tmp_contracts = $query->get();
            $client_codes = [];
            foreach ($tmp_contracts as $e) {
                if ($e->created_by != auth()->user()->id) {
                    array_push($client_codes, $e->client_code);
                }
            }
            if (count($client_codes) > 0) {
                return response()->json(['message' => 'Bạn không được xóa những hợp đồng có Mã khách hàng là ' . $client_codes . '!'], 200);
            }
            $query->delete();

            DB::commit();
            return response()->json(['message' => 'Xử lý thành công!'], 200);
        } catch (\Exception $e) {
            Log::error('ContractController deleteContractForm: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function show($id)
    {
        try {
            $contract = Contract::where('id', $id)->with('clients', 'salePerson')->first();
            return $contract;
        } catch (\Exception $e) {
            Log::error('ContractController show: ' . $e->getMessage());
            DB::rollBack();
            return response()->json([], 500);
        }
    }

    public function destroy($id)
    {
        try {
            $contract = Contract::find($id);
            if (!$contract) {
                return response()->json(['message' => 'Không tìm thấy hợp đồng!'], 404);
            }
            if ($contract->creator_id != auth()->user()->id) {
                return response()->json(['message' => 'Bạn không có quyền xóa bản ghi này!'], 400);
            }
            DB::beginTransaction();
            $contract->delete();
            DB::commit();
            return response()->json(['message' => 'Success'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ContractController destroy: ' . $e->getMessage());
            return response()->json([], 500);
        }
    }

    public function downloadFileSample()
    {
        try {
            $inputFileName = 'templates/contract_report.xlsx';
            $fileType = IOFactory::identify($inputFileName);
            $objReader = IOFactory::createReader($fileType);
            $objPHPExcel = $objReader->load($inputFileName);

            //lấy danh sách mã sản phẩm
            $sheetProduct = $objPHPExcel->setActiveSheetIndex(1);
            $row = 2;
            $products = Product::select('code', 'type')->get();
            foreach ($products as $k => $e) {
                $sheetProduct->setCellValue("A" . $row, $k + 1);
                $sheetProduct->setCellValue("B" . $row, $e->code);
                $sheetProduct->setCellValue("C" . $row, $e->type == Product::SIGN_TYPE_NEW ? Product::SIGN_TYPE[Product::SIGN_TYPE_NEW] : Product::SIGN_TYPE[Product::SIGN_TYPE_RENEW]);
                $row++;
            }
            $sheetProduct->getStyle('A2:C' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);

            //lấy danh sách nhân viên kinh doanh
            $sheetUser = $objPHPExcel->setActiveSheetIndex(2);
            $row = 2;
            $users = User::select('id', 'staff_code', 'name')->whereHas('user', function ($q) {
                return $q->where('department_id', Department::SALE_DEPARTMENT);
            })->get();
            foreach ($users as $k => $e) {
                $sheetUser->setCellValue("A" . $row, $k + 1);
                $sheetUser->setCellValue("B" . $row, $e->staff_code);
                $sheetUser->setCellValue("C" . $row, $e->name);
                $row++;
            }
            $sheetUser->getStyle('A2:C' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
            $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="contract_report.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (Exception $e) {
            Log::error("ContractController downloadFileSample: " . $row . $e->getMessage());
            return response()->json([], 500);
        }
    }

    /**
     * get data dashboard
     */

    // doanh thu 3 nam gan nhat
    public function getThreeYearChartData(Request $request)
    {
        try {
            $data = Dashboard::getThreeYearChartData($request);
            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('ContractController getThreeYearChartData' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi trong quá trình xử lý'], 500);
        }
    }

    //doanh thu theo từng sản phẩm
    public function getCurrentYearByProductChartData(Request $request)
    {
        try {
            $data = Dashboard::getCurrentYearByProductChartData($request);

            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('ContractController getCurrentYearByProductChartData' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi trong quá trình xử lý'], 500);
        }
    }

    //doanh thu theo từng team
    public function getCurrentYearByTeamChartData(Request $request)
    {
        try {
            $data = Dashboard::getCurrentYearByTeamChartData($request);

            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('ContractController getCurrentYearByTeamChartData' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi trong quá trình xử lý'], 500);
        }
    }

    //chi phí công tác theo từng team
    public function getExpenseByTeamChartData()
    {
        try {
            $data = Dashboard::getExpenseByTeamChartData();

            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('ContractController getExpenseByTeamChartData' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi trong quá trình xử lý'], 500);
        }
    }

    public function dashboardPublic(Request $request)
    {
        $best_seller = Dashboard::getBestSeller();
        // $top_product = Dashboard::getTopProduct();
        $goals = Dashboard::getGoals($request);

        return [
            "best_seller" => $best_seller,
            // 'top_product' => $top_product,
            'goals' => $goals
        ];
    }

    public function requestExportContract(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            //check xem có email được cấu hình xuất hóa đơn chưa
            $config = SystemConfig::select('content')->where('type', SystemConfig::EMAIL_REQUEST_EXPORT_CONTRACT)->first();
            if (!$config) {
                return response()->json(['message' => 'Chưa có email được cấu hình xuất hóa đơn'], 404);
            }
            if ($request->type == ContractPaymentHistory::BASE_PRODUCT) {
                $query = new Contract();
            } else {
                $query = new KEContract();
            }
            //check xem hợp đồng có tồn tại không
            $contract = $query->where('id', $id)->with('clients')->first();
            if (!$contract) {
                return response()->json(['message' => 'Hợp đồng không tồn tại'], 404);
            }
            //check quyền yêu cầu xuất hóa đơn
            if ($contract->sale_id != auth()->user()->id) {
                return response()->json(['message' => 'Bạn không có quyền yêu cầu xuất hóa đơn cho hợp đồng này'], 403);
            }
            //check trạng thái xuất hóa đơn của hợp đồng
            if ($contract->export_contract_status == ExportContractStatus::CONFIRM_REQUEST) {
                return response()->json(['message' => 'Yêu cầu đã được kế toán xác nhận'], 404);
            }
            //gửi mail yêu cầu xuất hóa đơn tới kế toán
            $receiveEmails = User::whereIn('email', explode(',', $config->content))->get();
            $sale_name = auth()->user()->name;
            foreach ($receiveEmails as $user) {
                $message = [
                    'subject' => "[VIETEC-QLDN]- Thông báo yêu cầu xuất hóa đơn cho hợp đồng",
                    'greeting' => "Xin chào, " . $user->name,
                    'body' => "Anh/Chị được nhân viên kinh doanh " . $sale_name . " yêu cầu xuất hóa đơn cho hợp đồng có Mã hợp đồng là: " . $contract->contract_code,
                    'actionTxt' => 'Xem chi tiết',
                    'actionUrl' =>  env('APP_URL') . '/contract/detail/' . $id
                ];
                $user->notify(new EmailNotification($message));
            }
            //cập nhật trạng thái hợp đồng 
            $query->where('id', $id)->update(['export_contract_status' => ExportContractStatus::REQUEST]);
            //lưu vết lịch sử
            if ($request->type == ContractPaymentHistory::BASE_PRODUCT) {
                $this->saveExportContractStatus($contract, ExportContractStatus::REQUEST, null);
            } else {
                $this->saveExportKEContractStatus($contract, ExportContractStatus::REQUEST, null);
            }
            DB::commit();
            return response()->json(['message' => 'Gửi email thành công'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ContractController getExpenseByTeamChartData' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi trong quá trình xử lý'], 500);
        }
    }

    public function confirmRequestExportContract(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            if (!$request->bill_number) {
                return response()->json(['message' => 'Bạn phải điền số hóa đơn'], 404);
            }
            //check quyền xuất hóa đơn
            if (!auth()->user()->hasAnyPermission(['approve_export_contract'])) {
                return response()->json(['message' => 'Bạn không có quyền xuất hóa đơn cho hợp đồng này'], 403);
            }
            if ($request->type == ContractPaymentHistory::BASE_PRODUCT) {
                $query = new Contract();
            } else {
                $query = new KEContract();
            }
            //check xem hợp đồng có tồn tại không
            $contract = $query->where('id', $id)->with('salePerson')->first();
            if (!$contract) {
                return response()->json(['message' => 'Hợp đồng không tồn tại'], 404);
            }
            //check trạng thái xuất hóa đơn của hợp đồng
            if (
                $contract->export_contract_status == ExportContractStatus::NOT_REQUEST_YET
            ) {
                return response()->json(['message' => 'Hợp đồng chưa được yêu cầu xuất hóa đơn'], 404);
            }
            if ($contract->export_contract_status == ExportContractStatus::CONFIRM_REQUEST) {
                return response()->json(['message' => 'Hợp đồng đã được xác nhận yêu cầu xuất hóa đơn'], 404);
            }
            //gửi mail confirm tới nhân viên kinh doanh
            $notifier = User::where('id', $contract->sale_id)->first();
            $message = [
                'subject' => "[VIETEC-QLDN]- Thông báo xác nhận yêu cầu xuất hóa đơn cho hợp đồng",
                'greeting' => "Xin chào, " . $contract['salePerson']['name'],
                'body' => "Anh/Chị được " . auth()->user()->name . " xác nhận yêu cầu xuất hóa đơn cho hợp đồng có Mã hợp đồng là: " . $contract->contract_code,
                'actionTxt' => 'Xem chi tiết',
                'actionUrl' =>  env('APP_URL') . '/contract/detail/' . $id
            ];
            $notifier->notify(new EmailNotification($message));
            //cập nhật số hóa đơn và trạng thái 
            $query->where('id', $id)->update(['bill_number' => $request->bill_number, 'export_contract_status' => ExportContractStatus::CONFIRM_REQUEST]);
            //lưu vết lịch sử
            if ($request->type == ContractPaymentHistory::BASE_PRODUCT) {
                $this->saveExportContractStatus($contract, ExportContractStatus::CONFIRM_REQUEST, $request->bill_number);
            } else {
                $this->saveExportKEContractStatus($contract, ExportContractStatus::CONFIRM_REQUEST, $request->bill_number);
            }
            DB::commit();
            return response()->json(['message' => 'Xác nhận yêu cầu xuất hóa đơn thành công'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ContractController getExpenseByTeamChartData' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi trong quá trình xử lý'], 500);
        }
    }

    public function reopenRequestExportContract(Request $request, $id)
    {
        DB::beginTransaction();
        try {
            //check quyền xuất hóa đơn
            if (!auth()->user()->hasAnyPermission(['approve_export_contract'])) {
                return response()->json(['message' => 'Bạn không có quyền xuất hóa đơn cho hợp đồng này'], 403);
            }
            if ($request->type == ContractPaymentHistory::BASE_PRODUCT) {
                $query = new Contract();
            } else {
                $query = new KEContract();
            }
            //check xem hợp đồng có tồn tại không
            $contract = $query->where('id', $id)->first();
            if (!$contract) {
                return response()->json(['message' => 'Hợp đồng không tồn tại'], 404);
            }
            //check trạng thái của hợp đồng
            if ($contract->export_contract_status != ExportContractStatus::CONFIRM_REQUEST) {
                return response()->json(['message' => 'Bạn không thể thao tác chức năng này'], 404);
            }
            //cập nhật trạng thái 
            $query->where('id', $id)->update(['export_contract_status' => ExportContractStatus::REOPEN_CONTRACT]);
            //lưu vết lịch sử
            if ($request->type == ContractPaymentHistory::BASE_PRODUCT) {
                $this->saveExportContractStatus($contract, ExportContractStatus::REOPEN_CONTRACT, $contract->bill_number);
            } else {
                $this->saveExportKEContractStatus($contract, ExportContractStatus::REOPEN_CONTRACT, $contract->bill_number);
            }
            DB::commit();
            return response()->json(['message' => 'Xác nhận yêu cầu xuất hóa đơn thành công'], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('ContractController getExpenseByTeamChartData' . $e->getMessage());
            return response()->json(['message' => 'Có lỗi trong quá trình xử lý'], 500);
        }
    }

    private function saveExportContractStatus($contract, $status, $bill_number)
    {
        $contractHistory = new ExportContractStatus();
        $contractHistory->contract_id = $contract->id;
        $contractHistory->created_by = auth()->user()->id;
        $contractHistory->export_contract_status = $status;
        $contractHistory->client_id = $contract->client_id;
        $contractHistory->product_id = $contract->product_id;
        $contractHistory->from_date = $contract->from_date;
        $contractHistory->to_date = $contract->to_date;
        $contractHistory->contract_code = $contract->contract_code;
        $contractHistory->contract_value = $contract->contract_value;
        $contractHistory->payment_amount = $contract->payment_amount;
        $contractHistory->received_money = $contract->received_money;
        $contractHistory->debt = $contract->debt;
        $contractHistory->unequal = $contract->unequal;
        $contractHistory->user_name_product = $contract->user_name_product;
        $contractHistory->bill_number = $bill_number ? $bill_number : $contract->bill_number;
        $contractHistory->sale_id = $contract->sale_id;
        $contractHistory->leader_id = $contract->leader_id;
        $contractHistory->company = $contract->company;
        $contractHistory->save();
    }

    private function saveExportKEContractStatus($contract,  $status, $bill_number)
    {
        $contractHistory = new KEContractStatusHistory();
        $contractHistory->ke_contract_id = $contract->id;
        $contractHistory->origin_ke_sales_packages_id = $contract->origin_ke_sales_packages_id;
        $contractHistory->created_by = auth()->user()->id;
        $contractHistory->export_contract_status = $status;
        $contractHistory->client_id = $contract->client_id;
        $contractHistory->product_id = $contract->product_id;
        $contractHistory->start_date = $contract->start_date;
        $contractHistory->end_date = $contract->end_date;
        $contractHistory->contract_code = $contract->contract_code;
        $contractHistory->contract_value = $contract->contract_value;
        $contractHistory->payment_amount = $contract->payment_amount;
        $contractHistory->received_money = $contract->received_money;
        $contractHistory->debt = $contract->debt;
        $contractHistory->unequal = $contract->unequal;
        $contractHistory->bill_number = $bill_number ? $bill_number : $contract->bill_number;
        $contractHistory->sale_id = $contract->sale_id;
        $contractHistory->leader_id = $contract->leader_id;
        $contractHistory->save();
    }

    public function exportContract(Request $request)
    {
        $inputFileName = 'templates/contract_report_base.xlsx';
        $fileType = IOFactory::identify($inputFileName);
        $objReader = IOFactory::createReader($fileType);
        $objPHPExcel = $objReader->load($inputFileName);
        $sheet = $objPHPExcel->setActiveSheetIndex(0);
        $row = 3;
        $query = Contract::with('clients', 'product',  'salePerson', 'leader', 'creator');
        $this->getCondition($request, $query);
        $contracts = $query->get();
        $contract_ids = [];
        foreach ($contracts as $k => $e) {
            array_push($contract_ids, $e->id);
            if ($e->vat) {
                $vat_money = ($e->vat) * $e->contract_value;
            } else {
                $vat_money = 0;
            }
            $sheet->setCellValue('A' . $row, $k + 1)->getStyle('A' . $row)->getAlignment()->setHorizontal('center');
            $sheet->setCellValue('B' . $row, $e->clients->code);
            $sheet->setCellValue('C' . $row, $e->clients->tax_code);
            $sheet->setCellValue('D' . $row, $e->invoice_name ? $e->invoice_name : $e->clients->name);
            $sheet->setCellValue('E' . $row, $e->buyer);
            $sheet->setCellValue('F' . $row, '');
            $sheet->setCellValue('G' . $row, '');
            $sheet->setCellValue('H' . $row, $e->invoice_address ? $e->invoice_address : $e->clients->address);
            $sheet->setCellValue('I' . $row, $e->creator->email . ',' . $e->salePerson->email . ',' . $e->leader->email);
            $sheet->setCellValue('J' . $row, $e->product->code);
            $sheet->setCellValue('K' . $row, $e->product->name);
            $sheet->setCellValue('L' . $row, 'bản');
            $sheet->setCellValue('M' . $row, $e->amount);
            $sheet->setCellValue('N' . $row, $e->payment_amount);
            $sheet->setCellValue('O' . $row, $e->unequal);
            $sheet->setCellValue('P' . $row, $e->contract_value);
            $sheet->setCellValue('Q' . $row, $e->contract_value * $e->amount);
            $sheet->setCellValue('R' . $row, $e->vat ? ($e->vat) * 100 . '%' : 'KCT');
            $sheet->setCellValue('S' . $row, $vat_money);
            $sheet->setCellValue('T' . $row, $e->contract_value + $vat_money);
            $row++;
        }
        DB::beginTransaction();
        Contract::whereIn('id', $contract_ids)->update(['invoice_export_date' => date('Y-m-d'), 'invoice_status' => Contract::EXPORTED_STATUS]);
        DB::commit();
        $sheet->getStyle('A3:U' . ($row - 1))->getBorders()->getAllBorders()->setBorderStyle(Style\Border::BORDER_THIN);
        $objWriter = IOFactory::createWriter($objPHPExcel, $fileType);
        try {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="Report.xlsx"');
            header('Cache-Control: max-age=0');
            $objWriter->save("php://output");
        } catch (\Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function dividePermission($query)
    {
        if (auth()->user()->user && auth()->user()->user->department_id == Department::SALE_DEPARTMENT) {
            if (auth()->user()->hasAnyPermission(['contract_list_management', 'contract_help_upload'])) {
                $query;
            } else {
                if (auth()->user()->user->position->is_leader == null) {
                    $dependent_position_id = auth()->user()->user->dependent_position_id;
                } else {
                    $dependent_position_id = auth()->user()->user->position_id;
                }
                $leaderId = UserWorking::getAllDependentPositionIdByPosition($dependent_position_id, []);
                $query->whereIn('position_leader_id', $leaderId);
            }
        } else {
            $query;
        }
    }

    private function getCondition($request, $query)
    {
        $query->when($request->province_id, function ($subQuery, $province_id) {
            $subQuery->whereHas('clients', function ($q) use ($province_id) {
                return $q->where('province_id', $province_id);
            });
        })->when($request->district_id, function ($subQuery, $district_id) {
            $subQuery->whereHas('clients', function ($q) use ($district_id) {
                return $q->where('district_id', $district_id);
            });
        })->when($request->product_id, function ($subQuery, $product_id) {
            $subQuery->whereHas('product', function ($q) use ($product_id) {
                return $q->where('product_id', $product_id);
            });
        })->when($request->school_name, function ($subQuery, $school_name) {
            $subQuery->whereHas('clients', function ($q) use ($school_name) {
                return $q->where('name', 'like', '%' . $school_name . '%');
            });
        })->when($request->client_code, function ($subQuery, $client_code) {
            $subQuery->whereHas('clients', function ($q) use ($client_code) {
                return $q->where('code', 'like', '%' . $client_code . '%');
            });
        });

        $query->when($request->team, function ($subQuery, $team) {
            return $subQuery->where('team', $team);
        })->when($request->company, function ($subQuery, $company) {
            return $subQuery->where('company', $company);
        })->when(isset($request->invoice_request), function ($subQuery) use ($request) {
            return $subQuery->where('invoice_request', $request->invoice_request);
        })->when(isset($request->invoice_status), function ($subQuery) use ($request) {
            if (!$request->invoice_status) {
                return $subQuery->where(function ($sq) use ($request) {
                    $sq->where('invoice_status', $request->invoice_status)->orWhereNull('invoice_status');
                });
            } else {
                return $subQuery->where('invoice_status', $request->invoice_status);
            }
        })->when($request->month, function ($subQuery, $month) {
            return $subQuery->whereMonth('sale_date', $month);
        })->when($request->year, function ($subQuery, $year) {
            return $subQuery->whereYear('sale_date', $year);
        })->when($request->created_at, function ($subQuery, $created_at) {
            return $subQuery->whereDate('created_at', $created_at);
        })->when(isset($request->selected_rows), function ($subQuery) use ($request) {
            return $subQuery->whereIn('id', $request->selected_rows);
        });
    }
    // Mobile
    public function indexMobile(Request $request)
    {
        try {
            $query  = Contract::with('clients.provinceBusinessMarket', 'clients.districtBusinessMarket', 'product', 'position', 'creator', 'paymentHistory', 'feedbackHistory');
            $this->dividePermission($query);
            $this->getCondition($request, $query);
            $rs = with(clone $query)->orderBy('created_at', 'DESC')->paginate(10);
            $pre_page = $request['pre_page'] ?? 10;
            $option = [
                'current_page' => $rs->currentPage(),
                'per_page' => $pre_page,
                'total' => $rs->lastpage()
            ];
            return (new APIJsonResponse)->responseSuccess((new ContractTransformer)->transforms($rs), null, $option);
        } catch (\Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function contractFormListMobile(Request $request)
    {
        try {
            $query = TmpContract::where('save_type', ImportContract::FORM_SAVE)->whereIn('status', [ImportContract::WAIT_STATUS, ImportContract::REJECT_STATUS])
                ->when($request->province_id, function ($subQuery, $province_id) {
                    return $subQuery->where('province_id', $province_id);
                })->when($request->district_id, function ($subQuery, $district_id) {
                    return $subQuery->where('district_id', $district_id);
                })->when($request->product_id, function ($subQuery, $product_id) {
                    return $subQuery->where('product_id', $product_id);
                })->when($request->team, function ($subQuery, $team) {
                    return $subQuery->where('team', $team);
                })->when($request->company, function ($subQuery, $company) {
                    return $subQuery->where('company', $company);
                })->when($request->status, function ($subQuery, $status) {
                    return $subQuery->where('status', $status);
                });
            if (auth()->user()->hasAnyPermission(['review_contract'])) {
                $query;
            } else {
                $query->where('created_by', auth()->user()->id);
            }

            $data = $query->with('createdBy', 'confirm_by', 'product', 'sale', 'province', 'district')->paginate(10);
            $pre_page = $request['pre_page'] ?? 10;
            $option = [
                'current_page' => $data->currentPage(),
                'per_page' => $pre_page,
                'total' => $data->lastpage()
            ];
            return (new APIJsonResponse)->responseSuccess((new TmpContractTransformer)->transforms($data), null, $option);
        } catch (\Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function showMobile(Request $request)
    {
        try {
            $contract = Contract::where('id', $request->id)->with('clients.provinceBusinessMarket', 'clients.districtBusinessMarket', 'clients', 'salePerson', 'paymentHistory', 'creator', 'importContract', 'importContract.confirmBy', 'product')->first();
            $show_btn_edit = false;
            if (!empty($contract) && auth()->user()->id == $contract->creator_id) {
                $show_btn_edit = true;
            }
            return (new APIJsonResponse)->responseSuccess((new ContractTransformer)->viewDetail($contract, $show_btn_edit));
        } catch (\Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function allFilterMobile(Request $request)
    {
        try {
            $user = auth()->user();
            if (
                $user->user
                && $user->user->position
                && $user->user->position->department_id == Department::SALE_DEPARTMENT
            ) {
                if (auth()->user()->hasAnyPermission(['contract_help_upload'])) {
                    $provinces = ProvinceBusinessMarket::with('districtBusinessMarket')
                        ->orderBy('name')
                        ->get();
                } else {
                    $area = TeamSaleArea::getProvincesAndDistrictsBySaleArea($user);
                    $provinceIds = array_unique($area['provinceIds']);
                    $provinces = ProvinceBusinessMarket::whereIn('province_id', $provinceIds)->with('districtBusinessMarket')
                        ->orderBy('name')
                        ->get();
                }
            } else {
                $provinces = ProvinceBusinessMarket::with('districtBusinessMarket')
                    ->orderBy('name')
                    ->get();
            }
            $products = Product::select('id', 'code', 'short_name')->orderBy('sort', 'ASC')->get();
            $teams = Position::year(date('Y'))->select('representatives', 'representatives as name', 'sort', 'id')->where('representatives', 'like', '%TEAM%')->orderBy('sort', 'ASC')->get();
            $companys = [];
            foreach (SystemConfig::COMPANY as $id => $name) {
                $companys[] = [
                    'id' => $id,
                    'name' => $name
                ];
            }
            $invoice_requests = Contract::invoiceRequest();
            $invoice_status = Contract::invoiceStatus();
            $review_status = Contract::reviewStatus();
            $types = Contract::getTypeClient();
            $data = [
                'provinces'         => (new ProvinceBusinessTransformer)->transforms($provinces),
                'products'          => $products,
                'teams'             => $teams,
                'companys'          => $companys,
                'invoice_requests'  => $invoice_requests,
                'invoice_status'    => $invoice_status,
                'review_status'     => $review_status,
                'types'             => $types
            ];
            return (new APIJsonResponse)->responseSuccess($data, null, null);
        } catch (\Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function updateMobile(Request $request)
    {
        DB::beginTransaction();
        try {
            $contract = Contract::find($request->id);
            if (!$contract) {
                return response()->json(['message' => 'Không tìm thấy hợp đồng!'], 404);
            }
            if (auth()->user()->id != $contract->creator_id) {
                return response()->json(['message' => 'Bạn không có quyền sửa bản ghi này!'], 400);
            }
            $contract->from_date = $request->from_date;
            $contract->to_date = $request->to_date;
            $contract->user_name_product = $request->user_name_product;
            $contract->invoice_status = $request->invoice_status;
            $contract->invoice_name = $request->invoice_name;
            $contract->invoice_address = $request->invoice_address;
            $contract->fb_school = $request->fb_school / 100;
            $contract->fb_district = $request->fb_district / 100;
            $contract->fb_province = $request->fb_province / 100;
            $contract->save();
            DB::commit();
            return (new APIJsonResponse)->responseSuccess($contract);
        } catch (\Exception $e) {
            DB::rollBack();
            return (new APIJsonResponse)->responseError();
        }
    }

    public function editTmpContractMobile(ContractUpdateRequest $request)
    {
        DB::beginTransaction();
        try {
            $contract = TmpContract::find($request->tmpContractId);
            if (!$contract) {
                return response()->json(['message' => 'Không tìm thấy hợp đồng!'], 404);
            }
            if (auth()->user()->id != $contract->created_by) {
                return response()->json(['message' => 'Bạn không có quyền sửa bản ghi này!'], 400);
            }
            TmpContract::store($request, $contract, $request->position_leader_id, $request->leader_id, $request->sale_id, $request->client_code, $request->product_category_id);
            DB::commit();
            return (new APIJsonResponse)->responseSuccess($contract);
        } catch (\Exception $e) {
            DB::rollBack();
            return (new APIJsonResponse)->responseError();
        }
    }

    public function storeMobile(Request $request)
    {
        DB::beginTransaction();
        try {
            $messages = [
                'staff_code.exists' => 'Mã nhân viên không tồn tại!',
            ];
            $validator = Validator::make($request->all(), [
                'staff_code' => 'required|max:255|exists:users',
            ], $messages);
            if ($validator->fails()) {
                return (new APIJsonResponse)->responseError(401, $validator->errors()->first(), null);
            }
            $position_leader = Position::year(date('Y'))->select('id', 'representatives')->where('representatives', $request->team)->first();
            $leader = UserWorking::where('status', true)->where('position_id', $position_leader->id)->first();
            $staff = User::where('staff_code', $request->staff_code)->first();
            $product = Product::where('id', $request->product_id)->first();
            //ktra xem khách hàng đã có chưa
            $client = Client::searchClient($request->province_id, $request->district_id, $request->client_code);
            if (!$client) {
                Client::store($request);
                $client_code = $request->client_code;
            } else {
                $client_code = $client->code;
                Client::updateClient($request, $client->id);
            }
            $model = new TmpContract();
            TmpContract::store($request, $model, $position_leader->id, $leader->user_id, $staff->id, $client_code, $product->product_category_id);
            $model->save();
            DB::commit();
            return (new APIJsonResponse)->responseSuccess($model);
        } catch (\Exception $e) {
            DB::rollBack();
            return (new APIJsonResponse)->responseError();
        }
    }

    public function paymentListMobile(Request $request)
    {
        try {
            if ($request->province_id && $request->district_id) {
                $query  = Contract::whereNotNull('debt')->where('debt', '!=', 0)->with('clients', 'product', 'position', 'creator', 'paymentHistory', 'feedbackHistory');
                $this->dividePermission($query);
                $this->getCondition($request, $query);
                $rs = with(clone $query)->orderBy('created_at', 'DESC')->paginate(5);
            } else {
                $rs = [];
            }

            $pre_page = $request['pre_page'] ?? 5;
            $option = [
                'current_page' => !empty($rs) ? $rs->currentPage() : 0,
                'per_page' => $pre_page,
                'total' => !empty($rs) ? $rs->lastpage() : 0
            ];
            return (new APIJsonResponse)->responseSuccess((new ContractTransformer)->payments($rs), null, $option);
        } catch (Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function paymentSaveMobile(Request $request)
    {
        DB::beginTransaction();
        try {
            $errors = [];
            $count = [];
            foreach ($request->payment_lists as $key => $item) {
                $e = (object) $item;
                //cập nhật bảng contracts
                if (!isset($e->payment_total) && !isset($e->payment_date)) {
                    continue;
                }

                if (!isset($e->payment_total) && isset($e->payment_date)) {
                    $errors = 'Số tiền thanh toán dòng ' . $key + 1 . ' không được bỏ trống';
                    return (new APIJsonResponse)->responseError(422, $errors, null, null);
                }

                if (isset($e->payment_total) && !isset($e->payment_date)) {
                    $errors = 'Ngày thanh toán dòng ' . $key + 1 . ' không được bỏ trống';
                    return (new APIJsonResponse)->responseError(422, $errors, null, null);
                }
                if (isset($e->payment_total) && isset($e->payment_date)) {
                    Contract::where('id', $e->id)->update(['received_money' => $e->payment_total + $e->received_money, 'debt' => $e->contract_value - $e->payment_total - $e->received_money]);
                    //thêm lịch sử thanh toán
                    $payment_history = new ContractPaymentHistory();
                    $payment_history->contract_id = $e->id;
                    $payment_history->contract_code = $e->contract_code;
                    $payment_history->current_contract_value = $e->contract_value;
                    $payment_history->payment_amount = $e->payment_amount;
                    $payment_history->current_received_money = $e->payment_total + $e->received_money;
                    $payment_history->current_debt = $e->contract_value - $e->payment_total - $e->received_money;
                    $payment_history->payment_date = $e->payment_date;
                    $payment_history->created_by = auth()->user()->id;
                    $payment_history->type = ContractPaymentHistory::BASE_PRODUCT;
                    $payment_history->save();
                    array_push($count, $key);
                }
            }

            DB::commit();
            return (new APIJsonResponse)->responseSuccess($request->payment_lists);
        } catch (\Exception $e) {
            DB::rollBack();
            return (new APIJsonResponse)->responseError();
        }
    }

    public function destroyMobile(Request $request)
    {
        DB::beginTransaction();
        try {
            $contract = Contract::find($request->id);
            if (!$contract) {
                return response()->json(['message' => 'Không tìm thấy hợp đồng!'], 404);
            }
            if ($contract->creator_id != auth()->user()->id) {
                return response()->json(['message' => 'Bạn không có quyền xóa bản ghi này!'], 400);
            }
            $contract->delete();
            DB::commit();
            return (new APIJsonResponse)->responseSuccess($contract);
        } catch (\Exception $e) {
            DB::rollBack();
            return (new APIJsonResponse)->responseError();
        }
    }

    public function deleteContractFormMobile(Request $request)
    {
        DB::beginTransaction();
        try {
            $query = TmpContract::where('id', $request->contract_ids);
            $tmp_contracts = $query->get();
            $client_codes = [];
            foreach ($tmp_contracts as $e) {
                if ($e->created_by != auth()->user()->id) {
                    array_push($client_codes, $e->client_code);
                }
            }
            if (count($client_codes) > 0) {
                return response()->json(['message' => 'Bạn không được xóa những hợp đồng có Mã khách hàng là ' . $client_codes . '!'], 200);
            }
            $query->delete();

            DB::commit();
            return (new APIJsonResponse)->responseSuccess($tmp_contracts);
        } catch (\Exception $e) {
            DB::rollBack();
            return (new APIJsonResponse)->responseError();
        }
    }

    public function approveContractFormMobile(Request $request)
    {
        try {
            if (!auth()->user()->hasAnyPermission(['review_contract'])) {
                $errors = 'Bạn không có quyền thực hiện hành động này này!';
                return (new APIJsonResponse)->responseError(403, $errors, null, null);
            }
            DB::beginTransaction();
            $query = TmpContract::where('id', $request->contract_ids)->where('confirm_by', null)->where('status', ImportContract::WAIT_STATUS);
            $tmp_contracts = $query->get();
            if (count($tmp_contracts) > 0) {
                $query->update(['status' => ImportContract::APPROVE_STATUS, 'confirm_by' => auth()->user()->id]);
            } else {
                $errors = 'Hợp đồng được duyệt không hợp lệ!';
                return (new APIJsonResponse)->responseError(404, $errors, null, null);
            }
            foreach ($tmp_contracts as $e) {
                $client = Client::searchClient($e->province_id, $e->district_id, $e->client_code);
                Contract::store($e, $client->id, $e->created_by, null);
            }
            DB::commit();
            return (new APIJsonResponse)->responseSuccess($tmp_contracts);
        } catch (\Exception $e) {
            DB::rollBack();
            return (new APIJsonResponse)->responseError();
        }
    }

    public function rejectContractFormMobile(Request $request)
    {
        try {
            if (!auth()->user()->hasAnyPermission(['review_contract'])) {
                $errors = 'Bạn không có quyền thực hiện hành động này này!';
                return (new APIJsonResponse)->responseError(403, $errors, null, null);
            }
            DB::beginTransaction();
            TmpContract::where('id', $request->contract_ids)->update(['status' => ImportContract::REJECT_STATUS, 'confirm_by' => auth()->user()->id, 'reason_refuse' => $request->reason_refuse]);
            DB::commit();
            return (new APIJsonResponse)->responseSuccess($request->contract_ids);
        } catch (\Exception $e) {
            DB::rollBack();
            return (new APIJsonResponse)->responseError();
        }
    }
}

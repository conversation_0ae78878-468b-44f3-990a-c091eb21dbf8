<?php

namespace App\Http\Controllers;

use App\Builder\NotificationBuilder;
use App\Builder\NotificationConfig;
use App\Events\ConfirmInOutEvent;
use App\Models\InOutExplanation;
use App\Models\PersonInOut;
use App\Models\Position;
use App\Models\User;
use App\Models\SystemConfig;
use App\Models\Response;
use App\Models\HanetCamUser;
use App\Notifications\InOutNotification;
use App\Providers\UtilityServiceProvider as u;
use App\Services\FirebaseNotification;
use App\Transformer\APIJsonResponse;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class InOutExplanationController extends Controller
{
    # thực hiện insert or update luôn
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required',
            'date' => 'required',
            'content' => 'required',
        ]);
        try {
            $params = $request->all();
            # chỉ được giải trình trong ngày
            if (strtotime($params['date']) != strtotime(date("Y-m-d"))) {
                return Response::formatResponse(config('apicode.WRONG_PARAMS'), $request->all());
            }
            $month = Carbon::parse($request->date)->format('Y-m');
            $config = SystemConfig::getExplain();
            $rule_status = 0;
            $explainMiss = InOutExplanation::explainMiss($params['user_id'], $month, $rule_status)->get();
            $configInoutExplain = $request->rule_status ? (int)$config[SystemConfig::EXPLAIN_ALLOW_MISS] : (int)$config[SystemConfig::EXPLAIN_ALLOW];
            if ($explainMiss && count($explainMiss) >= $configInoutExplain) {
                return response()->json(['message' => 'Bạn đã vượt quá số lần giải trình chấm công!'], 400);
            }
            $data = InOutExplanation::where('user_id', $params['user_id'])->where('date', $params['date'])->where('status', 1)->first();
            if ($data) {
                # update
                $model = InOutExplanation::find($data->id);
                $model->update($params);
            } else {
                # thêm mới
                InOutExplanation::create($params);

                # bắn notify cho người duyệt
                $managerId = u::getCurrentManager(false);
                $confirmUserId = $managerId ?: $params['user_id'];
                $userNoti = User::find($confirmUserId);
                $message = [
                    'title' => 'Duyệt giải trình chấm công cho nhân viên: ' . $request->user()->name,
                    'link' => '/checkin-checkout',
                    'content' => 'Bạn có thông báo duyệt giải trình chấm công cho nhân viên: ' . $request->user()->name,
                    'option' => 'add',
                    'type' => '3',
                ];
                $config = new NotificationConfig();
                $config->setUser($userNoti);
                $config->setMsg($message);
                $config->setNotification(function () use ($message) {
                    return new InOutNotification($message);
                });
                $config->setBroadcast(function () use ($userNoti) {
                    $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\InOutNotification")->first();
                    return new ConfirmInOutEvent($userNoti, $newMessage);
                });
                $config->setNotificationType(FirebaseNotification::PUSH_TYPE_IN_OUT_EXPLANATION);


                NotificationBuilder::getBuilder()
                    ->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT]);
            }
            return Response::formatResponse(config('apicode.SUCCESS'), $params);
        } catch (\Exception $e) {
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function confirm(Request $request)
    {
        DB::beginTransaction();
        try {
            $params = $request->all();
            $model = InOutExplanation::find($params['id']);
            $model->status = $params['option'];
            $model->confirm_by = $params['confirm_by'];
            $model->confirm_title = $params['confirm_title'];
            if ($params['option'] == 1) {
                $model->confirm_title = null;
            }

            $model->save();
            if ($params['option'] == 2) {
                # chèn thêm bản ghi đúng giờ sáng và chiều cho nhân sự luôn
                $date = $params['date'];
                $data = PersonInOut::where('aliasID', $params['staff_code'])->where('date', 'like', $date . '%')->first();
                $rowPersonInOut = new PersonInOut();
                $rowPersonInOut->date = $date . ' 08:01:01';
                $rowPersonInOut->personTitle = $data->personTitle;
                $rowPersonInOut->action_type = $data->action_type;
                $rowPersonInOut->detected_image_url = $data->detected_image_url;
                $rowPersonInOut->placeID = $data->placeID;
                $rowPersonInOut->deviceID = $data->deviceID;
                $rowPersonInOut->personName = $data->personName;
                $rowPersonInOut->aliasID = $data->aliasID;
                $rowPersonInOut->data_type = $data->data_type;
                $rowPersonInOut->personID = $data->personID;
                $rowPersonInOut->time = strtotime($date . ' 08:01:01') * 1000;
                $rowPersonInOut->personType = $data->personType;
                $rowPersonInOut->placeName = $data->placeName;
                $rowPersonInOut->hash = $data->hash;
                $rowPersonInOut->mask = $data->mask;
                $rowPersonInOut->deviceName = $data->deviceName;
                $rowPersonInOut->create_by = 2;
                $rowPersonInOut->save();

                $rowPersonInOut = new PersonInOut();
                $rowPersonInOut->date = $date . ' 17:01:01';
                $rowPersonInOut->personTitle = $data->personTitle;
                $rowPersonInOut->action_type = $data->action_type;
                $rowPersonInOut->detected_image_url = $data->detected_image_url;
                $rowPersonInOut->placeID = $data->placeID;
                $rowPersonInOut->deviceID = $data->deviceID;
                $rowPersonInOut->personName = $data->personName;
                $rowPersonInOut->aliasID = $data->aliasID;
                $rowPersonInOut->data_type = $data->data_type;
                $rowPersonInOut->personID = $data->personID;
                $rowPersonInOut->time = strtotime($date . ' 17:01:01') * 1000;
                $rowPersonInOut->personType = $data->personType;
                $rowPersonInOut->placeName = $data->placeName;
                $rowPersonInOut->hash = $data->hash;
                $rowPersonInOut->mask = $data->mask;
                $rowPersonInOut->deviceName = $data->deviceName;
                $rowPersonInOut->create_by = 2;
                $rowPersonInOut->save();
            } else if ($params['option'] == 1) {
                PersonInOut::where('aliasID', $params['staff_code'])->where('create_by', 2)->where('date', 'like', $params['date'] . '%')->delete();
            }
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $model);
        } catch (\Exception $e) {
            DB::rollBack();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function HRCreateRecord(Request $request)
    {
        DB::beginTransaction();
        try {
            $params = $request->all();

            $save_db = [];
            if ($params['timeWork'] == 2) {
                $save_db['checkin'] =  date('Y-m-d', strtotime($params['from_date'])) . ' 13:00:00';
                $save_db['checkout'] =  date('Y-m-d', strtotime($params['to_date'])) . ' 17:01:01';
            } else {
                if ($params['timeWork'] == 1) {
                    $save_db['checkin'] =  date('Y-m-d', strtotime($params['from_date'])) . ' 08:01:01';
                    $save_db['checkout'] =  date('Y-m-d', strtotime($params['to_date'])) . ' 12:00:00';
                } else {
                    $save_db['checkin'] =  date('Y-m-d', strtotime($params['from_date'])) . ' 08:01:01';
                    $save_db['checkout'] =  date('Y-m-d', strtotime($params['to_date'])) . ' 17:01:01';
                }
            }

            $userID = HanetCamUser::where('aliasID', $params['staff_code'])->first();

            $save_db_in = new PersonInOut();
            $save_db_out = new PersonInOut();
            $save_db_in->date = $save_db['checkin'];
            $save_db_in->action_type = "hr_create";
            $save_db_in->detected_image_url = "";
            $save_db_in->placeID = "10818";
            $save_db_in->deviceID = "C21024B545";
            $save_db_in->personName = $userID['name'];
            $save_db_in->aliasID = $params['staff_code'];
            $save_db_in->data_type = "log";
            $save_db_in->personID = $userID['personID'];
            $save_db_in->time = strtotime($save_db['checkin']) * 1000;
            $save_db_in->personType = 0;
            $save_db_in->placeName = "Công ty Vietec";
            $save_db_in->hash = "hash";
            $save_db_in->mask = '-1';
            $save_db_in->deviceName = "Phòng test";
            $save_db_in->create_by = 1;
            $save_db_in->save();

            $save_db_out->date = $save_db['checkout'];
            $save_db_out->action_type = "hr_create";
            $save_db_out->detected_image_url = "";
            $save_db_out->placeID = "10818";
            $save_db_out->deviceID = "C21024B545";
            $save_db_out->personName = $userID['name'];
            $save_db_out->aliasID = $params['staff_code'];
            $save_db_out->data_type = "log";
            $save_db_out->personID = $userID['personID'];
            $save_db_out->time = strtotime($save_db['checkin']) * 1000;
            $save_db_out->personType = 0;
            $save_db_out->placeName = "Công ty Vietec";
            $save_db_out->hash = "hash";
            $save_db_out->mask = '-1';
            $save_db_out->deviceName = "Phòng test";
            $save_db_out->create_by = 1;
            $save_db_out->save();
            //dd($save_db_in, $save_db_out);
            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $save_db_out);
        } catch (\Exception $e) {
            DB::rollBack();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function confirmMiss(Request $request)
    {
        DB::beginTransaction();
        try {
            $params = $request->all();
            $model = InOutExplanation::find($params['id']);
            $model->status = $params['option'];
            $model->confirm_by = $params['confirm_by'];
            $model->confirm_title = $params['confirm_title'];
            if ($params['option'] == 1) {
                $model->confirm_title = null;
            }

            $model->save();

            DB::commit();
            return Response::formatResponse(config('apicode.SUCCESS'), $model);
        } catch (\Exception $e) {
            DB::rollBack();
            return Response::formatResponse(config('apicode.INTERNAL_SERVER_ERROR'), $e->getMessage());
        }
    }

    public function approveMobile(Request $request)
    {
        try {
            $request->validate([
                'id' => 'required',
            ]);
            $explainId = $request->id;

            $user = auth()->user()->user;
            $model = InOutExplanation::find($explainId);
            if ($model->status == 2 || $model->status == 3) {
                return (new APIJsonResponse)->responseError(500, 'Giải trình đã được duyệt hoặc từ chối');
            }
            $model->status = 2;
            $model->confirm_by = $user->id;
            $userName = auth()->user()->name;
            $position = Position::year(date('Y'))->select('id', 'name')->where('id', auth()->user()->user->position_id)->first();
            $createdByTitle = $userName . ' - ' . $position->name;
            $model->confirm_title = $createdByTitle;
            $model->save();
            $target = User::select('id', 'staff_code')->where('id', $model->user_id)->first();
            $data = PersonInOut::where('aliasID', $target->staff_code)->where('date', 'like', $model->date . '%')->first();
            $rowPersonInOut = new PersonInOut();
            $rowPersonInOut->date = $model->date . ' 08:01:01';
            $rowPersonInOut->personTitle = $data->personTitle;
            $rowPersonInOut->action_type = $data->action_type;
            $rowPersonInOut->detected_image_url = $data->detected_image_url;
            $rowPersonInOut->placeID = $data->placeID;
            $rowPersonInOut->deviceID = $data->deviceID;
            $rowPersonInOut->personName = $data->personName;
            $rowPersonInOut->aliasID = $data->aliasID;
            $rowPersonInOut->data_type = $data->data_type;
            $rowPersonInOut->personID = $data->personID;
            $rowPersonInOut->time = strtotime($model->date . ' 08:01:01') * 1000;
            $rowPersonInOut->personType = $data->personType;
            $rowPersonInOut->placeName = $data->placeName;
            $rowPersonInOut->hash = $data->hash;
            $rowPersonInOut->mask = $data->mask;
            $rowPersonInOut->deviceName = $data->deviceName;
            $rowPersonInOut->create_by = 2;
            $rowPersonInOut->save();

            $rowPersonInOut = new PersonInOut();
            $rowPersonInOut->date = $model->date . ' 17:01:01';
            $rowPersonInOut->personTitle = $data->personTitle;
            $rowPersonInOut->action_type = $data->action_type;
            $rowPersonInOut->detected_image_url = $data->detected_image_url;
            $rowPersonInOut->placeID = $data->placeID;
            $rowPersonInOut->deviceID = $data->deviceID;
            $rowPersonInOut->personName = $data->personName;
            $rowPersonInOut->aliasID = $data->aliasID;
            $rowPersonInOut->data_type = $data->data_type;
            $rowPersonInOut->personID = $data->personID;
            $rowPersonInOut->time = strtotime($model->date . ' 17:01:01') * 1000;
            $rowPersonInOut->personType = $data->personType;
            $rowPersonInOut->placeName = $data->placeName;
            $rowPersonInOut->hash = $data->hash;
            $rowPersonInOut->mask = $data->mask;
            $rowPersonInOut->deviceName = $data->deviceName;
            $rowPersonInOut->create_by = 2;
            $rowPersonInOut->save();
            DB::commit();
            return (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function addOrEditExplainMobile(Request $request)
    {

        $request->validate([
            'content' => 'required',
        ]);
        try {
            $params = $request->all();
            # chỉ được giải trình trong ngày
            $user = auth()->user();
            $now = Carbon::now();
            $staffCode = $user->staff_code;
            $date = $now->toDateString();
            $checkIn = PersonInOut::select('id', 'date')
                ->where('aliasID', $staffCode)
                ->where('date', 'like', $date . '%')
                ->first();
            if (!$checkIn) {
                return (new APIJsonResponse)->responseError(500, 'Chưa chấm công vào');
            }

            $data = InOutExplanation::where('user_id', $user->id)->where('date', $date)->where('status', 1)->first();
            $params = [
                'user_id' => $user->id,
                'date' => $date,
                //'content' => $request->content, Báo lỗi nên tắt tạm
            ];
            if ($data) {
                # update
                $model = InOutExplanation::find($data->id);
                $model->update($params);
            } else {
                # thêm mới
                InOutExplanation::create($params);

                # bắn notify cho người duyệt
                $managerId = u::getCurrentManager(false);
                $confirmUserId = $managerId ?: $user->id;
                $userNoti = User::find($confirmUserId);
                $message = [
                    'title' => 'Duyệt giải trình chấm công cho nhân viên: ' . $user->name,
                    'content' => 'Bạn có thông báo duyệt giải trình chấm công cho nhân viên: ' . $request->user()->name,
                    'type' => '3',
                ];

                $config = new NotificationConfig();
                $config->setMsg($message);
                $config->setUser($userNoti);
                $config->setNotification(function () use ($message) {
                    return new InOutNotification($message);
                });
                $config->setBroadcast(function () use ($userNoti) {
                    $newMessage = $userNoti->unreadNotifications()->where("type", "App\Notifications\InOutNotification")->first();
                    return new ConfirmInOutEvent($userNoti, $newMessage);
                });
                $config->setNotificationType(FirebaseNotification::PUSH_TYPE_IN_OUT_EXPLANATION);
                NotificationBuilder::getBuilder()
                    ->sendMessage($config, [NotificationBuilder::MOBILE, NotificationBuilder::EVENT]);
            }
            return (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function rollbackExplainMobile(Request $request)
    {
        try {
            $request->validate([
                'id' => 'required',
            ]);
            $explainId = $request->id;
            $model = InOutExplanation::find($explainId);
            if ($model->status != 2 && $model->status != 3) {
                return (new APIJsonResponse)->responseError(500, 'Chưa duyệt hoặc từ chối giải trình');
            }
            $model->confirm_title = null;
            $model->status = 1;
            $model->save();
            $target = User::select('id', 'staff_code')->where('id', $model->user_id)->first();
            PersonInOut::where('aliasID', $target->staff_code)->where('create_by', 2)->where('date', 'like', $model->date . '%')->delete();
            DB::commit();
            return (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }

    public function rejectExplainMobile(Request $request)
    {
        try {
            $request->validate([
                'id' => 'required',
            ]);
            $explainId = $request->id;
            $model = InOutExplanation::find($explainId);
            if ($model->status != 1) {
                return (new APIJsonResponse)->responseError(500, 'Giải trình đã được duyệt hoặc từ chối');
            }
            $model->status = 3;
            $model->save();
            DB::commit();
            return (new APIJsonResponse)->responseSuccess();
        } catch (\Exception $e) {
            return (new APIJsonResponse)->responseError();
        }
    }
}

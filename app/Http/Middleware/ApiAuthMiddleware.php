<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Tymon\JWTAuth\Exceptions\JWTException;
use Tymon\JWTAuth\Exceptions\TokenExpiredException;
use <PERSON><PERSON>\JWTAuth\Exceptions\TokenInvalidException;
use Tymon\JWTAuth\Http\Middleware\BaseMiddleware;

class ApiAuthMiddleware extends BaseMiddleware
{

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        try {
            $this->auth->parseToken()->authenticate();
            if (Auth::guard('api')->check()) {
                return $next($request);
            }

            return response()->json(['error' => __('Vui lòng đăng nhập hệ thống!'), 'code' => 401]);
        } catch (JWTException $e) {
            dd($e);
            if ($e instanceof TokenInvalidException) {
                return response()->json(['error' => __('messages.errors.auth.token.invalid'), 'code' => 401]);
            }
            if ($e instanceof TokenExpiredException) {
                return response()->json(['error' => __('Lượt đăng nhập hết hạn, vui lòng đăng nhập lại hệ thống!'), 'code' => 401]);
            }

            return response()->json(['error' => __('Có lỗi trong quá trình xử lý!'), 'code' => 401]);
        }
    }
}

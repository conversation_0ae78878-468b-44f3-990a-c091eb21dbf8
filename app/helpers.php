<?php
require dirname(__DIR__) . '/vendor/autoload.php';

if (!function_exists('vietec_isJson')) {
    function vietec_isJson($string)
    {
        if (is_string($string)) {
            json_decode($string);
            return (json_last_error() == JSON_ERROR_NONE);
        } else {
            return false;
        }
    }
}

if (!function_exists('vietec_get_weekday')) {
    function vietec_get_weekday($weekday = 0)
    {
        $weekdays = (int) $weekday;
        $response = 'Sunday';
        switch ($weekday) {
            case 1:
                $response = 'Monday';
                break;
            case 2:
                $response = 'Tuesday';
                break;
            case 3:
                $response = 'Wednesday';
                break;
            case 4:
                $response = 'Thursday';
                break;
            case 5:
                $response = 'Friday';
                break;
            case 6:
                $response = 'Saturday';
                break;
        }
        return $response;
    }
}

if (!function_exists('vietec_convert_unicode')) {
    function vietec_convert_unicode($str)
    {
        $str = preg_replace("/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)/", 'a', $str);
        $str = preg_replace("/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/", 'e', $str);
        $str = preg_replace("/(ì|í|ị|ỉ|ĩ)/", 'i', $str);
        $str = preg_replace("/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)/", 'o', $str);
        $str = preg_replace("/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)/", 'u', $str);
        $str = preg_replace("/(ỳ|ý|ỵ|ỷ|ỹ)/", 'y', $str);
        $str = preg_replace("/(đ)/", 'd', $str);
        $str = preg_replace("/(À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ)/", 'A', $str);
        $str = preg_replace("/(È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ)/", 'E', $str);
        $str = preg_replace("/(Ì|Í|Ị|Ỉ|Ĩ)/", 'I', $str);
        $str = preg_replace("/(Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ)/", 'O', $str);
        $str = preg_replace("/(Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ)/", 'U', $str);
        $str = preg_replace("/(Ỳ|Ý|Ỵ|Ỷ|Ỹ)/", 'Y', $str);
        $str = preg_replace("/(Đ)/", 'D', $str);
        $str = str_replace(" ", "", str_replace("&*#39;", "", $str));
        return $str;
    }
}

if (!function_exists('vietec_clean_unicode')) {
    function vietec_clean_unicode($str)
    {
        $str = preg_replace("/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)/", 'a', $str);
        $str = preg_replace("/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/", 'e', $str);
        $str = preg_replace("/(ì|í|ị|ỉ|ĩ)/", 'i', $str);
        $str = preg_replace("/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)/", 'o', $str);
        $str = preg_replace("/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)/", 'u', $str);
        $str = preg_replace("/(ỳ|ý|ỵ|ỷ|ỹ)/", 'y', $str);
        $str = preg_replace("/(đ)/", 'd', $str);
        $str = preg_replace("/(À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ)/", 'A', $str);
        $str = preg_replace("/(È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ)/", 'E', $str);
        $str = preg_replace("/(Ì|Í|Ị|Ỉ|Ĩ)/", 'I', $str);
        $str = preg_replace("/(Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ)/", 'O', $str);
        $str = preg_replace("/(Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ)/", 'U', $str);
        $str = preg_replace("/(Ỳ|Ý|Ỵ|Ỷ|Ỹ)/", 'Y', $str);
        $str = preg_replace("/(Đ)/", 'D', $str);
        $str = str_replace("&*#39;", "", $str);
        return $str;
    }
}

if (!function_exists('vietec_format_number')) {
    function vietec_format_number($number, $vnd = false)
    {
        $ps = '';
        $p = (string) $number;
        $dem = 0;
        $x = strlen($p);
        for ($i = $x - 1; $i >= 0; $i--) {
            if ($dem % 3 == 0) {
                $x = ',' . $ps;
                $ps = $x;
            }
            $x = $p[$i] . $ps;
            $ps = $x;
            $dem++;
        }
        $ps = rtrim($ps, ',');
        return $vnd ? $ps . ' đ' : $ps;
    }
}

if (!function_exists('vietec_format_number2')) {
    function vietec_format_number2($number, $vnd = false)
    {
        if (empty($number)) return '';
        $ps = '';
        $p = (string) $number;
        $dem = 0;
        $x = strlen($p);
        for ($i = $x - 1; $i >= 0; $i--) {
            if ($dem % 3 == 0) {
                $x = ',' . $ps;
                $ps = $x;
            }
            $x = $p[$i] . $ps;
            $ps = $x;
            $dem++;
        }
        $ps = rtrim($ps, ',');
        return $vnd ? $ps . ' đ' : $ps;
    }
}

if (!function_exists('vietec_format_date')) {
    function vietec_format_date($date, $format = 'd-m-Y')
    {
        if (empty($date)) return '';
        return date_format(date_create($date), $format);
    }
}

if (!function_exists('vietec_convert_array_to_map')) {
    function vietec_convert_array_to_map($array, $key = 'id', $push = false)
    {
        if (empty($array)) return [];
        $res = [];
        foreach ($array as $index => $item) {
            $k = is_callable($key) ? call_user_func($key, $item, $index) : (is_object($item) ? $item->{$key} : $item[$key]);
            if ($push) {
                $res[$k][] = $item;
            } else {
                $res[$k] = $item;
            }
        }
        return $res;
    }
}

if (!function_exists('format_index_date')) {
    function format_index_date($index = 0)
    {
        $index = (int) $index;
        $response = '';
        switch ($index) {
            case 1:
                $response = 'T2';
                break;
            case 2:
                $response = 'T3';
                break;
            case 3:
                $response = 'T4';
                break;
            case 4:
                $response = 'T5';
                break;
            case 5:
                $response = 'T6';
                break;
            case 6:
                $response = 'T7';
                break;
            case 7:
                $response = 'CN';
                break;
        }
        return $response;
    }
}

function isArrEmptyOrNull($arr)
{
    if (is_array($arr) && count($arr) > 0) {
        return false;
    }
    return true;
}

if (!function_exists('api_response')) {
    function api_response(bool $success, string $message = '', $data = null, int $code = 200, $error = null, $transformer = null, $paginate = false)
    {
        ///dd($data);
        $response = [
            'success' => $success,
            'code'    => $code,
            'message' => $message,
        ];

        if ($success) {
            if ($data) {
                if ($transformer) {
                    $fractal = new \League\Fractal\Manager();
                    if ($data instanceof \Illuminate\Support\Collection) {
                        $resource = new \League\Fractal\Resource\Collection($data, new $transformer);
                    } elseif (is_array($data) && isset($data[0])) {
                        $resource = new \League\Fractal\Resource\Collection($data, new $transformer);
                    } else {
                        $resource = new \League\Fractal\Resource\Item($data, new $transformer);
                    }
                    if($paginate){
                        $response['data'] = $fractal->createData($resource)->toArray()['data'];
                    }else{
                        $response['data'] = $fractal->createData($resource)->toArray()['data'];
                    }

                    
                } else {
                    $response['data'] = $data;
                }
            }
        } else {
            if ($error != null) {
                $response['error'] = $error;
            }
        }

        return response()->json($response, $code);
    }
}
if (!function_exists('vietec_sanitize_username')) {
    function vietec_sanitize_username($username) 
    {
        // Remove whitespace and convert to lowercase
        $username = trim(strtolower($username));
        
        // Only allow alphanumeric, dots, underscores and hyphens
        $username = preg_replace('/[^a-z0-9\._-]/', '', $username);
        
        // Remove consecutive special chars
        $username = preg_replace('/[._-]{2,}/', '', $username);
        
        return $username;
    }
}